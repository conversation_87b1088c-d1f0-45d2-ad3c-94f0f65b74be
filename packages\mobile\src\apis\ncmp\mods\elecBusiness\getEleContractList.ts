/**
 * @description 获取电子业务列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<Array<defs.ncmp.EleContract>>;
export const path = '/wx-ncmp/elecbusiness/getElecBusinessList';
export const method = 'POST';
export const request = (
  data: defs.ncmp.EleContract,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.EleContract,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
