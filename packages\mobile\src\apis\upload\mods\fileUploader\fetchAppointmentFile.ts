/**
 * @description 查询预约业务办理附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 当前用户的accountId */
  accountId: string;
  /** 预约办理的主键id */
  bookingId?: number;
  /** 文件主键id */
  ebmBookingImageId?: number;
  /** 作为临时主键的uuid */
  uuid: string;
}

export type Result = defs.upload.HttpResult<
  Array<defs.upload.CtEbmBookingImageDTO>
>;
export const path = '/wx-upload/appointment/fetchAppointmentFile';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
