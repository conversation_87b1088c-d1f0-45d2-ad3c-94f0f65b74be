/**
 * @description excelByTime
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** time */
  time: string;
}

export type Result = any;
export const path = '/user-server/api/excel/time/{time}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
