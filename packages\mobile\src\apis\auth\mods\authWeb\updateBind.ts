/**
 * @description 绑定账号
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.auth.GlobalResult<defs.auth.BindAccountResp>;
export const path = '/enterprise-auth/web/updateBind';
export const method = 'POST';
export const request = (
  data: defs.auth.BindAccountParams,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.auth.BindAccountParams,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
