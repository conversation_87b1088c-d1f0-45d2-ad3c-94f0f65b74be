/**
 * @description 工资列表查询接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** empId */
  empId?: string;
  /** endMonth */
  endMonth?: number;
  /** openId */
  openId?: string;
  /** pageNum */
  pageNum?: number;
  /** pageSize */
  pageSize?: number;
  /** startMonth */
  startMonth?: number;
}

export type Result = defs.pact.WageListRespBean;
export const path = '/yc-wepact-mobile/busi/getPersonWages';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
