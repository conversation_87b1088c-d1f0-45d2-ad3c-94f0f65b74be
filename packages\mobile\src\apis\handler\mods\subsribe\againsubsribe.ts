/**
 * @description 微信再次关注
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId?: string;
  /** cmpToken */
  cmpToken?: string;
}

export type Result = string;
export const path = '/handler-service/againsubsribe';
export const method = 'POST';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
