# MaterialList 材料列表组件

一个完整的材料列表管理组件，自动获取材料清单并为每个材料提供上传下载功能。

## 功能特性

### 🚀 核心功能
- ✅ **自动获取材料**: 根据业务子类型ID自动获取材料清单
- ✅ **批量管理**: 统一管理多个材料的上传下载
- ✅ **状态同步**: 自动同步已上传文件状态
- ✅ **数据验证**: 完整的数据验证和错误处理

### 🎨 用户体验
- ✅ **清晰布局**: 序号标识，层次分明
- ✅ **状态反馈**: 加载、错误、空状态的完整处理
- ✅ **响应式设计**: 适配不同设备屏幕
- ✅ **动画效果**: 流畅的进入动画

### 🛡️ 技术特性
- ✅ **TypeScript**: 完整的类型安全
- ✅ **性能优化**: 智能的数据获取和缓存
- ✅ **错误恢复**: 支持重试机制
- ✅ **可配置**: 支持只读模式和自定义配置

## 使用方法

### 基础用法

```tsx
import { MaterialList } from '@components'

const MyComponent = () => {
  const handleMaterialChange = (materials) => {
    console.log('材料变化:', materials)
  }

  return (
    <MaterialList
      busSubtypeId="461"
      businessId="business-uuid"
      onMaterialChange={handleMaterialChange}
    />
  )
}
```

### 只读模式

```tsx
<MaterialList
  busSubtypeId="461"
  businessId="business-uuid"
  readonly={true}
/>
```

### 自定义样式

```tsx
<MaterialList
  busSubtypeId="461"
  businessId="business-uuid"
  className="custom-material-list"
/>
```

## API 参考

### MaterialListProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| busSubtypeId | string | - | 业务子类型ID |
| businessId | string | - | 业务UUID（必填） |
| readonly | boolean | false | 是否只读模式 |
| onMaterialChange | (materials: MaterialData[]) => void | - | 材料变化回调 |
| className | string | '' | 自定义样式类名 |

## 组件状态

### 加载状态
```tsx
// 显示加载提示
<View className={styles.loading}>
  <Text className={styles.loadingText}>加载材料列表中...</Text>
</View>
```

### 错误状态
```tsx
// 显示错误信息和重试按钮
<View className={styles.error}>
  <Text className={styles.errorText}>获取材料列表失败</Text>
  <View className={styles.retryBtn} onClick={handleRetry}>
    <Text className={styles.retryText}>重试</Text>
  </View>
</View>
```

### 空状态
```tsx
// 显示空状态提示
<View className={styles.empty}>
  <Text className={styles.emptyText}>暂无材料信息</Text>
  <Text className={styles.emptyTip}>请先选择业务类型和业务项目</Text>
</View>
```

## 数据流

### 1. 初始化流程

```mermaid
graph TD
    A[组件挂载] --> B[检查busSubtypeId]
    B --> C[调用材料列表API]
    C --> D[获取材料数据]
    D --> E[调用已上传文件API]
    E --> F[合并文件数据]
    F --> G[渲染材料列表]
```

### 2. 文件上传流程

```mermaid
graph TD
    A[用户选择文件] --> B[MaterialUpload组件处理]
    B --> C[上传到微信服务器]
    C --> D[上传到业务服务器]
    D --> E[更新文件状态]
    E --> F[通知父组件]
    F --> G[更新材料列表]
```

## 样式定制

### 主题配置

```scss
.container {
  &.theme-blue {
    .indexNumber {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    }
  }
  
  &.theme-green {
    .indexNumber {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    }
  }
  
  &.theme-purple {
    .indexNumber {
      background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
    }
  }
}
```

### 响应式设计

```scss
@media (max-width: 480px) {
  .header {
    padding: 20px 16px;
  }
  
  .title {
    font-size: 32px;
  }
  
  .materialList {
    padding: 0 16px;
  }
}
```

## 集成示例

### 在表单中使用

```tsx
import { Form, MaterialList } from '@components'

const AppointmentForm = () => {
  const form = useForm()
  const [materials, setMaterials] = useState([])

  const columns = [
    // 其他表单项...
    {
      showLine: false,
      render: () => <FormLabel level={2} title='材料列表' />
    },
    {
      showLine: false,
      render: () => (
        <MaterialList
          busSubtypeId={form.watch('busContent')}
          businessId={uuid}
          onMaterialChange={setMaterials}
        />
      )
    }
  ]

  return <Form columns={columns} />
}
```

### 数据验证

```tsx
const validateMaterials = (materials) => {
  const requiredMaterials = materials.filter(m => m.isRequired)
  const missingMaterials = requiredMaterials.filter(m => !m.files?.length)
  
  if (missingMaterials.length > 0) {
    const names = missingMaterials.map(m => m.materialsName).join('、')
    Taro.showToast({ 
      title: `请上传必填材料：${names}`, 
      icon: 'none' 
    })
    return false
  }
  
  return true
}
```

## 错误处理

### API错误处理

```tsx
try {
  const result = await pact.appoint.getMaterialList.request({
    subTypeId: busSubtypeId,
    openId
  })
  
  if (result.code !== '200') {
    throw new Error(result.message || '获取材料列表失败')
  }
} catch (error) {
  setError(error.message)
  Taro.showToast({ title: error.message, icon: 'none' })
}
```

### 网络错误恢复

```tsx
const handleRetry = useCallback(() => {
  setError('')
  fetchMaterials()
}, [fetchMaterials])
```

## 性能优化

### 1. 数据缓存

```tsx
const fetchMaterials = useCallback(async () => {
  // 缓存逻辑
}, [busSubtypeId, openId])
```

### 2. 组件懒加载

```tsx
const MaterialUpload = lazy(() => import('../material-upload'))
```

### 3. 虚拟滚动

对于大量材料的情况，可以考虑使用虚拟滚动：

```tsx
import { VirtualList } from '@tarojs/components'

<VirtualList
  height={500}
  itemData={materials}
  itemCount={materials.length}
  itemSize={200}
>
  {({ index, data }) => (
    <MaterialUpload material={data[index]} />
  )}
</VirtualList>
```

## 最佳实践

### 1. 数据管理

```tsx
// 使用 useReducer 管理复杂状态
const [state, dispatch] = useReducer(materialReducer, initialState)
```

### 2. 错误边界

```tsx
<ErrorBoundary fallback={<ErrorFallback />}>
  <MaterialList />
</ErrorBoundary>
```

### 3. 测试覆盖

```tsx
// 单元测试示例
describe('MaterialList', () => {
  it('should render materials correctly', () => {
    render(<MaterialList busSubtypeId="461" businessId="uuid" />)
    expect(screen.getByText('材料清单')).toBeInTheDocument()
  })
})
```

## 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持自动获取材料列表
- ✅ 集成MaterialUpload组件
- ✅ 完整的状态管理和错误处理

## 技术支持

如有问题或建议，请联系开发团队。
