/**
 * @description 预约列表接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** createDt */
  createDt?: string;
  /** empId */
  empId?: string;
  /** openId */
  openId?: string;
  /** pageNo */
  pageNo?: string;
}

export type Result = defs.pact.AppointmentListBean;
export const path = '/yc-wepact-mobile/appoint/getAppointmentList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
