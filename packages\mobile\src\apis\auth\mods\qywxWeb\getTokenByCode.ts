/**
 * @description 根据企业微信oauth2返回的code获取token和cropid
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 企业微信oauth2返回的code */
  code: string;
}

export type Result = defs.auth.GlobalResult<defs.auth.TokenByCode>;
export const path = '/enterprise-auth/web/qywx/getTokenByCode';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
