/// <reference path="./users/api.d.ts" />
/// <reference path="./upload/api.d.ts" />
/// <reference path="./dynamicsForms/api.d.ts" />
/// <reference path="./handler/api.d.ts" />
/// <reference path="./hss/api.d.ts" />
/// <reference path="./pact/api.d.ts" />
/// <reference path="./ncmp/api.d.ts" />
/// <reference path="./auth/api.d.ts" />

type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};
