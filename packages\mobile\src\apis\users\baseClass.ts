class Bankcard {
  /** bankurl */
  bankurl = '';

  /** id */
  id = undefined;

  /** remake */
  remake = '';
}

class BankcardInfo {
  /** code */
  code = '';

  /** message */
  message = '';
}

class BaseData {
  /** key */
  key = '';

  /** value */
  value = '';
}

class CertificationInfo {
  /** certificationId */
  certificationId = undefined;

  /** certificationName */
  certificationName = '';

  /** certificationType */
  certificationType = '';

  /** isDel */
  isDel = false;
}

class ChildrenEducation {
  /** childrenBirthDate */
  childrenBirthDate = '';

  /** childrenCardNumber */
  childrenCardNumber = '';

  /** childrenCardType */
  childrenCardType = undefined;

  /** childrenId */
  childrenId = undefined;

  /** childrenName */
  childrenName = '';

  /** childrenProportion */
  childrenProportion = undefined;

  /** childrenRelation */
  childrenRelation = false;

  /** countryId */
  countryId = undefined;

  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** eduCountry */
  eduCountry = '';

  /** eduEndDate */
  eduEndDate = '';

  /** eduPhase */
  eduPhase = undefined;

  /** eduSchoolName */
  eduSchoolName = '';

  /** eduStartDate */
  eduStartDate = '';

  /** eduStopDate */
  eduStopDate = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** updateDate */
  updateDate = '';
}

class CityInfo {
  /** cityId */
  cityId = undefined;

  /** cityLevel */
  cityLevel = '';

  /** cityName */
  cityName = '';

  /** countryId */
  countryId = undefined;

  /** isDel */
  isDel = false;

  /** phonetic */
  phonetic = '';
}

class ContinuingEduInfo {
  /** certificationAuthority */
  certificationAuthority = '';

  /** certificationDate */
  certificationDate = '';

  /** certificationName */
  certificationName = '';

  /** certificationNumber */
  certificationNumber = '';

  /** continuingEduType */
  continuingEduType = undefined;

  /** createDate */
  createDate = '';

  /** deductionAmount */
  deductionAmount = undefined;

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** eduEndDate */
  eduEndDate = '';

  /** eduId */
  eduId = undefined;

  /** eduPhase */
  eduPhase = undefined;

  /** eduStartDate */
  eduStartDate = '';

  /** eduType */
  eduType = undefined;

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** updateDate */
  updateDate = '';
}

class CountryInfo {
  /** countryId */
  countryId = undefined;

  /** countryName */
  countryName = '';

  /** isDel */
  isDel = false;
}

class EmpSalary {
  /** classId */
  classId = '';

  /** custName */
  custName = '';

  /** empId */
  empId = '';

  /** f1 */
  f1 = '';

  /** f10 */
  f10 = '';

  /** f2 */
  f2 = '';

  /** f3 */
  f3 = '';

  /** pages */
  pages = '';

  /** sendId */
  sendId = '';

  /** sendMonth */
  sendMonth = '';

  /** wageTaxStatus */
  wageTaxStatus = '';
}

class EndowmentInsuranceInfo {
  /** checkCode */
  checkCode = '';

  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** distinguishNum */
  distinguishNum = '';

  /** employeeId */
  employeeId = '';

  /** endDate */
  endDate = '';

  /** flag */
  flag = false;

  /** insuranceId */
  insuranceId = undefined;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** monthAmount */
  monthAmount = undefined;

  /** preSalary */
  preSalary = undefined;

  /** updateDate */
  updateDate = '';

  /** yearAmount */
  yearAmount = undefined;
}

class EssentialInfo {
  /** annualDeduction */
  annualDeduction = '';

  /** countryId */
  countryId = undefined;

  /** createDate */
  createDate = '';

  /** custId */
  custId = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** hasSpouse */
  hasSpouse = undefined;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** spouseCardNumber */
  spouseCardNumber = '';

  /** spouseCardType */
  spouseCardType = undefined;

  /** spouseName */
  spouseName = '';

  /** taxpayerAddress */
  taxpayerAddress = '';

  /** taxpayerCardNumber */
  taxpayerCardNumber = '';

  /** taxpayerCardType */
  taxpayerCardType = undefined;

  /** taxpayerEmail */
  taxpayerEmail = '';

  /** taxpayerId */
  taxpayerId = undefined;

  /** taxpayerName */
  taxpayerName = '';

  /** taxpayerNumber */
  taxpayerNumber = '';

  /** taxpayerPhone */
  taxpayerPhone = '';

  /** updateDate */
  updateDate = '';
}

class EventFlag {
  /** childFlag */
  childFlag = false;

  /** continueFlag */
  continueFlag = false;

  /** essentialFlag */
  essentialFlag = false;

  /** healthFlag */
  healthFlag = false;

  /** houseLoanFlag */
  houseLoanFlag = false;

  /** houseRentalFlag */
  houseRentalFlag = false;

  /** pensionFlag */
  pensionFlag = false;

  /** supportFlag */
  supportFlag = false;
}

class EventInfo {
  /** deductionAmount */
  deductionAmount = '';

  /** endDate */
  endDate = '';

  /** eventDescribe */
  eventDescribe = '';

  /** eventId */
  eventId = undefined;

  /** eventName */
  eventName = '';

  /** eventQuota */
  eventQuota = '';

  /** isDel */
  isDel = false;

  /** startDate */
  startDate = '';
}

class ExecuteResult {
  /** code */
  code = undefined;

  /** data */
  data = new ChildrenEducation();

  /** message */
  message = '';
}

class FeedBack {
  /** content */
  content = '';

  /** title */
  title = '';
}

class GetBankcardInfo {
  /** accountEmployeeName */
  accountEmployeeName = '';

  /** bankAcct */
  bankAcct = '';

  /** bankId */
  bankId = '';

  /** bankName */
  bankName = '';

  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';

  /** empBankCardId */
  empBankCardId = '';

  /** empId */
  empId = '';

  /** openAddress */
  openAddress = '';

  /** openBankName */
  openBankName = '';

  /** provinceId */
  provinceId = '';

  /** provinceName */
  provinceName = '';
}

class HealthInsuranceInfo {
  /** createDate */
  createDate = '';

  /** deductedAmount */
  deductedAmount = undefined;

  /** deductionMonth */
  deductionMonth = '';

  /** distinguishNum */
  distinguishNum = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** insuranceId */
  insuranceId = undefined;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** monthAmount */
  monthAmount = undefined;

  /** startDate */
  startDate = '';

  /** updateDate */
  updateDate = '';

  /** yearAmount */
  yearAmount = undefined;
}

class HouseBasicInfo {
  /** id */
  id = undefined;

  /** type */
  type = undefined;

  /** updateDate */
  updateDate = '';
}

class HouseLoanInfo {
  /** createDate */
  createDate = '';

  /** deductionAmount */
  deductionAmount = undefined;

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** employeeId */
  employeeId = '';

  /** endRepaymentDate */
  endRepaymentDate = '';

  /** flag */
  flag = false;

  /** houseAddress */
  houseAddress = '';

  /** houseCredentialsNum */
  houseCredentialsNum = '';

  /** houseCredentialsType */
  houseCredentialsType = undefined;

  /** houseFirstLoan */
  houseFirstLoan = undefined;

  /** houseId */
  houseId = undefined;

  /** houseLoanBank */
  houseLoanBank = '';

  /** houseLoanNum */
  houseLoanNum = '';

  /** houseLoanType */
  houseLoanType = undefined;

  /** isDel */
  isDel = false;

  /** isOneself */
  isOneself = false;

  /** isSync */
  isSync = false;

  /** loanPeriod */
  loanPeriod = undefined;

  /** startRepaymentDate */
  startRepaymentDate = '';

  /** updateDate */
  updateDate = '';
}

class HouseRentalInfo {
  /** createDate */
  createDate = '';

  /** deductionAmount */
  deductionAmount = undefined;

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** employeeId */
  employeeId = '';

  /** endLeaseDate */
  endLeaseDate = '';

  /** flag */
  flag = false;

  /** houseAddress */
  houseAddress = '';

  /** houseId */
  houseId = undefined;

  /** huoseLeaseType */
  huoseLeaseType = undefined;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** lessorCardNum */
  lessorCardNum = '';

  /** lessorCardType */
  lessorCardType = undefined;

  /** lessorName */
  lessorName = '';

  /** rentalNum */
  rentalNum = '';

  /** startLeaseDate */
  startLeaseDate = '';

  /** updateDate */
  updateDate = '';

  /** workCity */
  workCity = undefined;
}

class Martyrs {
  /** key */
  key = '';

  /** value */
  value = '';
}

class MedicalInfo {
  /** burdenAmount */
  burdenAmount = undefined;

  /** countryId */
  countryId = undefined;

  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** patientCardNumber */
  patientCardNumber = '';

  /** patientCardType */
  patientCardType = undefined;

  /** patientId */
  patientId = undefined;

  /** patientName */
  patientName = '';

  /** patientType */
  patientType = undefined;

  /** totalAmount */
  totalAmount = undefined;

  /** updateDate */
  updateDate = '';
}

class MoreChildrenEducation {
  /** childrenBirthDate */
  childrenBirthDate = '';

  /** childrenCardNumber */
  childrenCardNumber = '';

  /** childrenCardType */
  childrenCardType = undefined;

  /** childrenId */
  childrenId = undefined;

  /** childrenName */
  childrenName = '';

  /** childrenProportion */
  childrenProportion = undefined;

  /** childrenRelation */
  childrenRelation = false;

  /** countryId */
  countryId = undefined;

  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** eduCountry */
  eduCountry = '';

  /** eduEndDate */
  eduEndDate = '';

  /** eduPhase */
  eduPhase = undefined;

  /** eduSchoolName */
  eduSchoolName = '';

  /** eduStartDate */
  eduStartDate = '';

  /** eduStopDate */
  eduStopDate = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** updateDate */
  updateDate = '';

  /** updateTime */
  updateTime = '';
}

class MoreContinuingEduInfo {
  /** certificationAuthority */
  certificationAuthority = '';

  /** certificationDate */
  certificationDate = '';

  /** certificationName */
  certificationName = '';

  /** certificationNumber */
  certificationNumber = '';

  /** continuingEduType */
  continuingEduType = undefined;

  /** createDate */
  createDate = '';

  /** deductionAmount */
  deductionAmount = undefined;

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** eduEndDate */
  eduEndDate = '';

  /** eduId */
  eduId = undefined;

  /** eduPhase */
  eduPhase = undefined;

  /** eduStartDate */
  eduStartDate = '';

  /** eduType */
  eduType = undefined;

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** updateDate */
  updateDate = '';

  /** updateTime */
  updateTime = '';
}

class MoreSupportInfo {
  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isOnlyChild */
  isOnlyChild = false;

  /** isSync */
  isSync = false;

  /** monthDeduction */
  monthDeduction = undefined;

  /** shareType */
  shareType = undefined;

  /** supportBirthDate */
  supportBirthDate = '';

  /** supportCardNum */
  supportCardNum = '';

  /** supportCardType */
  supportCardType = undefined;

  /** supportCountryId */
  supportCountryId = undefined;

  /** supportId */
  supportId = undefined;

  /** supportName */
  supportName = '';

  /** supportRelation */
  supportRelation = undefined;

  /** tSupportInfoList */
  tSupportInfoList = [];

  /** updateDate */
  updateDate = '';
}

class Preferential {
  /** certificate */
  certificate = '';

  /** cmpToken */
  cmpToken = '';

  /** code */
  code = '';

  /** creatTime */
  creatTime = '';

  /** empId */
  empId = '';

  /** img */
  img = '';

  /** martyrId */
  martyrId = '';

  /** martyrvlaue */
  martyrvlaue = '';
}

class PsnCust {
  /** classId */
  classId = '';

  /** custName */
  custName = '';

  /** dataId */
  dataId = '';

  /** empId */
  empId = '';

  /** f18 */
  f18 = '';

  /** f33 */
  f33 = '';

  /** f9 */
  f9 = '';

  /** payAddress */
  payAddress = '';

  /** sendId */
  sendId = '';

  /** taxMonth */
  taxMonth = '';

  /** taxPayerType */
  taxPayerType = '';
}

class PsnCustTotal {
  /** f18 */
  f18 = '';

  /** f33 */
  f33 = '';

  /** f9 */
  f9 = '';

  /** pages */
  pages = '';
}

class PsnDetailParams {
  /** cmpToken */
  cmpToken = '';

  /** dataId */
  dataId = '';

  /** empId */
  empId = '';
}

class ReceiveBankcardInfo {
  /** accountEmployeeName */
  accountEmployeeName = '';

  /** bankAcct */
  bankAcct = '';

  /** bankId */
  bankId = '';

  /** cityId */
  cityId = '';

  /** cmpToken */
  cmpToken = '';

  /** empId */
  empId = '';

  /** openAddress */
  openAddress = '';

  /** openBankName */
  openBankName = '';

  /** provinceId */
  provinceId = '';
}

class ReceiveBaseData {
  /** pageNum */
  pageNum = '';

  /** typeId */
  typeId = '';
}

class ReceiveEmp {
  /** cmpToken */
  cmpToken = '';

  /** empId */
  empId = '';

  /** sendMonth */
  sendMonth = '';
}

class ReceiveParams {
  /** cmpToken */
  cmpToken = '';

  /** empId */
  empId = '';

  /** endMonth */
  endMonth = '';

  /** id */
  id = undefined;

  /** pageNum */
  pageNum = '';

  /** startMonth */
  startMonth = '';
}

class RequestPsnDetail {
  /** empName */
  empName = '';

  /** f12 */
  f12 = '';

  /** f13 */
  f13 = '';

  /** f16 */
  f16 = '';

  /** f17 */
  f17 = '';

  /** f18 */
  f18 = '';

  /** f19 */
  f19 = '';

  /** f20 */
  f20 = '';

  /** f21 */
  f21 = '';

  /** f25 */
  f25 = '';

  /** f33 */
  f33 = '';

  /** f34 */
  f34 = '';

  /** f35 */
  f35 = '';

  /** f36 */
  f36 = '';

  /** f37 */
  f37 = '';

  /** f38 */
  f38 = '';

  /** f44 */
  f44 = '';

  /** f55 */
  f55 = '';

  /** f56 */
  f56 = '';

  /** f57 */
  f57 = '';

  /** f58 */
  f58 = '';

  /** f63 */
  f63 = '';

  /** f67 */
  f67 = '';

  /** f68 */
  f68 = '';

  /** f69 */
  f69 = '';

  /** f8261 */
  f8261 = '';

  /** f8362 */
  f8362 = '';

  /** f8564 */
  f8564 = '';

  /** f8665 */
  f8665 = '';

  /** f8766 */
  f8766 = '';

  /** f9 */
  f9 = '';

  /** f9total */
  f9total = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardType */
  idCardType = '';

  /** maxtax */
  maxtax = '';

  /** mintax */
  mintax = '';

  /** pages */
  pages = '';

  /** taxPayerType */
  taxPayerType = '';
}

class ResponseEntity {
  /** body */
  body = undefined;

  /** statusCode */
  statusCode = undefined;

  /** statusCodeValue */
  statusCodeValue = undefined;
}

class SupportInfo {
  /** createDate */
  createDate = '';

  /** deductionMonth */
  deductionMonth = '';

  /** deductionType */
  deductionType = undefined;

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isOnlyChild */
  isOnlyChild = false;

  /** isSync */
  isSync = false;

  /** monthDeduction */
  monthDeduction = undefined;

  /** shareType */
  shareType = undefined;

  /** supportBirthDate */
  supportBirthDate = '';

  /** supportCardNum */
  supportCardNum = '';

  /** supportCardType */
  supportCardType = undefined;

  /** supportCountryId */
  supportCountryId = undefined;

  /** supportId */
  supportId = undefined;

  /** supportName */
  supportName = '';

  /** supportRelation */
  supportRelation = undefined;

  /** updateDate */
  updateDate = '';
}

class TSupportInfo {
  /** createDate */
  createDate = '';

  /** employeeId */
  employeeId = '';

  /** flag */
  flag = false;

  /** isDel */
  isDel = false;

  /** isSync */
  isSync = false;

  /** tSupportCardNum */
  tSupportCardNum = '';

  /** tSupportCardType */
  tSupportCardType = undefined;

  /** tSupportCountryId */
  tSupportCountryId = undefined;

  /** tSupportId */
  tSupportId = undefined;

  /** tSupportName */
  tSupportName = '';

  /** updateDate */
  updateDate = '';
}

class TemplateMsg {
  /** empId */
  empId = '';

  /** errorList */
  errorList = new FeedBack();

  /** first */
  first = '';

  /** idCard */
  idCard = '';

  /** keyword1 */
  keyword1 = '';

  /** keyword2 */
  keyword2 = '';

  /** keyword3 */
  keyword3 = '';

  /** messageId */
  messageId = '';

  /** remark */
  remark = '';

  /** typeCode */
  typeCode = '';
}

export const users = {
  Bankcard,
  BankcardInfo,
  BaseData,
  CertificationInfo,
  ChildrenEducation,
  CityInfo,
  ContinuingEduInfo,
  CountryInfo,
  EmpSalary,
  EndowmentInsuranceInfo,
  EssentialInfo,
  EventFlag,
  EventInfo,
  ExecuteResult,
  FeedBack,
  GetBankcardInfo,
  HealthInsuranceInfo,
  HouseBasicInfo,
  HouseLoanInfo,
  HouseRentalInfo,
  Martyrs,
  MedicalInfo,
  MoreChildrenEducation,
  MoreContinuingEduInfo,
  MoreSupportInfo,
  Preferential,
  PsnCust,
  PsnCustTotal,
  PsnDetailParams,
  ReceiveBankcardInfo,
  ReceiveBaseData,
  ReceiveEmp,
  ReceiveParams,
  RequestPsnDetail,
  ResponseEntity,
  SupportInfo,
  TSupportInfo,
  TemplateMsg,
};
