/**
 * @description 业务详细信息接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** businessId */
  businessId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.BusinessDetailBean;
export const path = '/yc-wepact-mobile/busi/getBusinessDetail';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
