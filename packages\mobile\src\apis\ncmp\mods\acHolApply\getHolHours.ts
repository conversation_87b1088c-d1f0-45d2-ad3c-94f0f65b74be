/**
 * @description 获取申请时长
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<ObjectMap<string, ObjectMap>>;
export const path = '/wx-ncmp/eos/hol/apply/getHolHours';
export const method = 'POST';
export const request = (
  data: defs.ncmp.AcHolApply,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.AcHolApply,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
