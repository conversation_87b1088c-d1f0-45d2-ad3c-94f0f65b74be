/**
 * @description 具体项目
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId?: string;
  /** typeId */
  typeId?: string;
}

export type Result = defs.pact.ThingItemsBean;
export const path = '/yc-wepact-mobile/appoint/getConcreteItems';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
