/**
 * @description 通过openId查询是否同意了最新隐私协议
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openId */
  openId: string;
}

export type Result = defs.ncmp.PrivacyRegisterDTO;
export const path = '/wx-ncmp/privacy/isRegisted';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
