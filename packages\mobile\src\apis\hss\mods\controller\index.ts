/**
 * @description Controller
 */
import * as authentication from './authentication';
import * as paySuccess from './paySuccess';
import * as headCallback from './headCallback';
import * as postCallback from './postCallback';
import * as putCallback from './putCallback';
import * as deleteCallback from './deleteCallback';
import * as optionsCallback from './optionsCallback';
import * as patchCallback from './patchCallback';
import * as getAuthByIdCard from './getAuthByIdCard';
import * as getAuthByOpenid from './getAuthByOpenid';
import * as getBasicInfoByOpenid from './getBasicInfoByOpenid';
import * as getHsDataStatus from './getHsDataStatus';
import * as getHsType from './getHsType';
import * as getVcode from './getVcode';
import * as insertBasicInfo from './insertBasicInfo';
import * as isExpire from './isExpire';
import * as orders from './orders';
import * as selectIdCardByOpenid from './selectIdCardByOpenid';
import * as sendSMS from './sendSMS';
import * as sendSMSBig from './sendSMSBig';
import * as sendSMSPersonal from './sendSMSPersonal';
import * as sendTemplateMsg from './sendTemplateMsg';
import * as updateAuthByIDCard from './updateAuthByIDCard';

export {
  authentication,
  paySuccess,
  headCallback,
  postCallback,
  putCallback,
  deleteCallback,
  optionsCallback,
  patchCallback,
  getAuthByIdCard,
  getAuthByOpenid,
  getBasicInfoByOpenid,
  getHsDataStatus,
  getHsType,
  getVcode,
  insertBasicInfo,
  isExpire,
  orders,
  selectIdCardByOpenid,
  sendSMS,
  sendSMSBig,
  sendSMSPersonal,
  sendTemplateMsg,
  updateAuthByIDCard,
};
