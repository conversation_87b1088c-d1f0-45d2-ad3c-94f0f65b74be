import { useState, useEffect, useCallback } from 'react'
import { View, Text, Image, Button } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { upload } from '@apis/upload'
import { getGlobalData } from '@utils'
import { BaseUrl } from '@utils/request'
import { isWx } from '@utils'
import { wxConfigManager } from '@utils/wx-config'
import styles from './index.module.scss'

// 图标资源
import UploadIcon from '@assets/icon/upload.png'
import DownloadIcon from '@assets/icon/download.png'
import DeleteIcon from '@assets/icon/delete.png'
import FileIcon from '@assets/icon/file.png'
import ImageIcon from '@assets/icon/image.png'

// 材料类型枚举
export enum MaterialType {
  IMAGE = 'image',
  DOCUMENT = 'document',
  PDF = 'pdf'
}

// 文件状态枚举
export enum FileStatus {
  UPLOADING = 'uploading',
  SUCCESS = 'success',
  ERROR = 'error'
}

// 材料数据接口
export interface MaterialData {
  materialsId: string
  materialsName: string
  isOriginal: string
  materialsAccount: number
  isReturn: string
  isRequired?: boolean
  files?: UploadedFile[]
}

// 上传文件接口
export interface UploadedFile {
  id?: string
  name: string
  url?: string
  size?: number
  type?: string
  status: FileStatus
  progress?: number
  uploadTime?: string
}

// 组件属性接口
export interface MaterialUploadProps {
  /** 材料数据 */
  material: MaterialData
  /** 业务UUID */
  businessId: string
  /** 是否只读模式 */
  readonly?: boolean
  /** 文件变化回调 */
  onFileChange?: (files: UploadedFile[]) => void
  /** 自定义样式类名 */
  className?: string
}

const MaterialUpload: React.FC<MaterialUploadProps> = ({
  material,
  businessId,
  readonly = false,
  onFileChange,
  className = ''
}) => {
  const [files, setFiles] = useState<UploadedFile[]>(material.files || [])
  const [uploading, setUploading] = useState(false)
  const { openId, accountId } = getGlobalData<'account'>('account')

  // 获取文件类型图标
  const getFileIcon = useCallback((fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    if (['jpg', 'jpeg', 'png', 'gif', 'webp'].includes(ext || '')) {
      return ImageIcon
    }
    return FileIcon
  }, [])

  // 格式化文件大小
  const formatFileSize = useCallback((size: number) => {
    if (size < 1024) return `${size}B`
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
    return `${(size / (1024 * 1024)).toFixed(1)}MB`
  }, [])

  // 微信选择文件
  const chooseWxFile = useCallback(async () => {
    if (!isWx()) {
      Taro.showToast({ title: '请在微信中打开', icon: 'none' })
      return
    }

    try {
      const localIds = await wxConfigManager.chooseImage({
        count: 9 - files.length, // 最多选择9张
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera']
      })

      if (localIds && localIds.length > 0) {
        uploadWxImages(localIds)
      }
    } catch (error) {
      console.error('选择图片失败:', error)
      Taro.showToast({ title: '选择图片失败', icon: 'none' })
    }
  }, [files.length])

  // 上传微信图片
  const uploadWxImages = useCallback(async (localIds: string[]) => {
    setUploading(true)

    for (const localId of localIds) {
      try {
        // 创建临时文件记录
        const tempFile: UploadedFile = {
          id: `temp_${Date.now()}_${Math.random()}`,
          name: `image_${Date.now()}.jpg`,
          status: FileStatus.UPLOADING,
          progress: 0
        }

        setFiles(prev => [...prev, tempFile])

        try {
          // 上传到微信服务器
          const serverId = await wxConfigManager.uploadImage(localId, 1)

          // 上传到业务服务器
          const result = await upload.fileUploader.uploadAppointmentFileByForm.request({
            businessId,
            materialsId: material.materialsId,
            serverId,
            openId,
            accountId
          })

          if (result.code === '200') {
            const uploadedFile: UploadedFile = {
              id: result.resultObj?.fileId,
              name: result.resultObj?.fileName || tempFile.name,
              url: result.resultObj?.fileUrl,
              size: result.resultObj?.fileSize,
              type: 'image',
              status: FileStatus.SUCCESS,
              uploadTime: new Date().toLocaleString()
            }

            setFiles(prev => prev.map(f =>
              f.id === tempFile.id ? uploadedFile : f
            ))

            Taro.showToast({ title: '上传成功', icon: 'success' })
          } else {
            throw new Error(result.message || '上传失败')
          }
        } catch (error) {
          console.error('上传失败:', error)
          setFiles(prev => prev.map(f =>
            f.id === tempFile.id ? { ...f, status: FileStatus.ERROR } : f
          ))
          Taro.showToast({ title: '上传失败', icon: 'none' })
        }
      } catch (error) {
        console.error('处理文件失败:', error)
      }
    }

    setUploading(false)
  }, [businessId, material.materialsId, openId, accountId])

  // 下载文件
  const downloadFile = useCallback(async (file: UploadedFile) => {
    if (!file.url) {
      Taro.showToast({ title: '文件地址无效', icon: 'none' })
      return
    }

    try {
      if (isWx()) {
        // 微信环境下载
        try {
          await wxConfigManager.downloadImage(file.id || '', 1)
          Taro.showToast({ title: '保存成功', icon: 'success' })
        } catch (error) {
          console.error('微信下载失败:', error)
          Taro.showToast({ title: '保存失败', icon: 'none' })
        }
      } else {
        // H5环境下载
        const link = document.createElement('a')
        link.href = file.url
        link.download = file.name
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        Taro.showToast({ title: '下载成功', icon: 'success' })
      }
    } catch (error) {
      console.error('下载失败:', error)
      Taro.showToast({ title: '下载失败', icon: 'none' })
    }
  }, [])

  // 删除文件
  const deleteFile = useCallback(async (file: UploadedFile) => {
    try {
      if (file.id && file.status === FileStatus.SUCCESS) {
        await upload.fileUploader.deleteAppointmentFile.request({
          bookImageId: Number(file.id)
        })
      }

      setFiles(prev => prev.filter(f => f.id !== file.id))
      Taro.showToast({ title: '删除成功', icon: 'success' })
    } catch (error) {
      console.error('删除失败:', error)
      Taro.showToast({ title: '删除失败', icon: 'none' })
    }
  }, [])

  // 预览图片
  const previewImage = useCallback(async (file: UploadedFile) => {
    if (file.type === 'image' && file.url) {
      try {
        if (isWx()) {
          // 微信环境使用微信预览
          const imageUrls = files.filter(f => f.type === 'image' && f.url).map(f => f.url!)
          await wxConfigManager.previewImage(file.url, imageUrls)
        } else {
          // H5环境使用Taro预览
          Taro.previewImage({
            current: file.url,
            urls: files.filter(f => f.type === 'image' && f.url).map(f => f.url!)
          })
        }
      } catch (error) {
        console.error('预览图片失败:', error)
        Taro.showToast({ title: '预览失败', icon: 'none' })
      }
    }
  }, [files])

  // 文件变化时通知父组件
  useEffect(() => {
    onFileChange?.(files)
  }, [files, onFileChange])

  return (
    <View className={`${styles.container} ${className}`}>
      {/* 材料信息头部 */}
      <View className={styles.header}>
        <View className={styles.materialInfo}>
          <Text className={styles.materialName}>
            {material.materialsName}
            {material.isRequired && <Text className={styles.required}>*</Text>}
          </Text>
          <View className={styles.materialDetails}>
            <Text className={styles.detail}>
              {material.isOriginal === '1' ? '原件' : '复印件'}
            </Text>
            <Text className={styles.detail}>
              数量：{material.materialsAccount}
            </Text>
            <Text className={styles.detail}>
              {material.isReturn === '1' ? '需返还' : '不返还'}
            </Text>
          </View>
        </View>
      </View>

      {/* 文件列表 */}
      <View className={styles.fileList}>
        {files.map((file) => (
          <View key={file.id} className={styles.fileItem}>
            <View className={styles.fileInfo}>
              <Image 
                className={styles.fileIcon} 
                src={getFileIcon(file.name)}
              />
              <View className={styles.fileDetails}>
                <Text className={styles.fileName}>{file.name}</Text>
                {file.size && (
                  <Text className={styles.fileSize}>
                    {formatFileSize(file.size)}
                  </Text>
                )}
                {file.uploadTime && (
                  <Text className={styles.uploadTime}>
                    {file.uploadTime}
                  </Text>
                )}
              </View>
            </View>

            {/* 文件状态 */}
            <View className={styles.fileStatus}>
              {file.status === FileStatus.UPLOADING && (
                <Text className={styles.uploading}>上传中...</Text>
              )}
              {file.status === FileStatus.ERROR && (
                <Text className={styles.error}>上传失败</Text>
              )}
              {file.status === FileStatus.SUCCESS && (
                <View className={styles.actions}>
                  <Button 
                    className={styles.actionBtn}
                    onClick={() => previewImage(file)}
                  >
                    <Image className={styles.actionIcon} src={FileIcon} />
                    预览
                  </Button>
                  <Button 
                    className={styles.actionBtn}
                    onClick={() => downloadFile(file)}
                  >
                    <Image className={styles.actionIcon} src={DownloadIcon} />
                    下载
                  </Button>
                  {!readonly && (
                    <Button 
                      className={styles.deleteBtn}
                      onClick={() => deleteFile(file)}
                    >
                      <Image className={styles.actionIcon} src={DeleteIcon} />
                      删除
                    </Button>
                  )}
                </View>
              )}
            </View>
          </View>
        ))}
      </View>

      {/* 上传按钮 */}
      {!readonly && (
        <View className={styles.uploadSection}>
          <Button 
            className={styles.uploadBtn}
            onClick={chooseWxFile}
            disabled={uploading || files.length >= 9}
          >
            <Image className={styles.uploadIcon} src={UploadIcon} />
            <Text className={styles.uploadText}>
              {uploading ? '上传中...' : '上传材料'}
            </Text>
          </Button>
          <Text className={styles.uploadTip}>
            支持图片格式，最多上传9个文件
          </Text>
        </View>
      )}
    </View>
  )
}

export default MaterialUpload
