/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-15 14:11:45
 * @LastAuthor: 王正荣
 * @LastTime: 2021-09-18 09:59:15
 * @message: 
 */
import { FunctionComponent } from 'react'
import { View, Text, Button, Image } from '@tarojs/components'
import Detail from '@assets/icon/icon-detail.png'
import Cancel from '@assets/icon/icon-cancel.png'
import styles from './index.module.scss'

interface Buttons {
  title?: string
  icon?: string
  type?: string
  size?: number
  onClick?: () => void
}

const Buttons: FunctionComponent<Buttons> = props => {
  const { title, icon, size, type, onClick } = props
  return (
    <Button className={icon === 'cancel' ? styles.cancel_wrap:  styles.wrap} onClick={onClick}>
      <Image className={icon === 'cancel' ? styles.icon_wrap: styles.icon} src={icon === 'cancel' ? Cancel: Detail}  />
      <Text className={styles.text}>{title}</Text>
    </Button>
  )
}

export default Buttons
