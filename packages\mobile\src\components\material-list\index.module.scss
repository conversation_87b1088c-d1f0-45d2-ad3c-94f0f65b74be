// 材料列表组件样式
.container {
  width: 100%;
  background: #f8f9fa;
  min-height: 200px;
}

// 头部
.header {
  background: #ffffff;
  padding: 24px 20px;
  margin-bottom: 16px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.title {
  font-size: 36px;
  font-weight: 600;
  color: #333333;
  line-height: 1.3;
  margin-bottom: 8px;
  display: block;
}

.subtitle {
  font-size: 26px;
  color: #666666;
  line-height: 1.4;
  display: block;
}

// 材料列表
.materialList {
  padding: 0 20px;
}

.materialItem {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.materialIndex {
  flex-shrink: 0;
  margin-right: 16px;
  margin-top: 20px;
}

.indexNumber {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #ffffff;
  font-size: 24px;
  font-weight: 600;
  border-radius: 50%;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.materialContent {
  flex: 1;
  min-width: 0;
}

// 加载状态
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 12px;
  margin: 20px;
}

.loadingText {
  font-size: 28px;
  color: #666666;
  position: relative;
  
  &::after {
    content: '';
    display: inline-block;
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #666666;
    margin-left: 8px;
    animation: loading-dots 1.4s infinite ease-in-out;
  }
}

@keyframes loading-dots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 错误状态
.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  background: #ffffff;
  border-radius: 12px;
  margin: 20px;
  text-align: center;
}

.errorText {
  font-size: 28px;
  color: #ff4d4f;
  line-height: 1.4;
  margin-bottom: 20px;
  display: block;
}

.retryBtn {
  padding: 12px 24px;
  background: #1890ff;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: #40a9ff;
    transform: translateY(-1px);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.retryText {
  font-size: 26px;
  color: #ffffff;
  font-weight: 500;
}

// 空状态
.empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  background: #ffffff;
  border-radius: 12px;
  margin: 20px;
  text-align: center;
}

.emptyText {
  font-size: 32px;
  color: #999999;
  line-height: 1.4;
  margin-bottom: 12px;
  display: block;
}

.emptyTip {
  font-size: 24px;
  color: #cccccc;
  line-height: 1.4;
  display: block;
}

// 底部
.footer {
  background: #ffffff;
  padding: 20px;
  margin: 20px;
  margin-bottom: 0;
  border-radius: 12px;
  border-left: 4px solid #1890ff;
}

.footerTip {
  font-size: 24px;
  color: #666666;
  line-height: 1.5;
  display: block;
}

// 响应式设计
@media (max-width: 480px) {
  .header {
    padding: 20px 16px;
    margin-bottom: 12px;
  }
  
  .title {
    font-size: 32px;
  }
  
  .subtitle {
    font-size: 24px;
  }
  
  .materialList {
    padding: 0 16px;
  }
  
  .materialItem {
    margin-bottom: 16px;
  }
  
  .materialIndex {
    margin-right: 12px;
    margin-top: 16px;
  }
  
  .indexNumber {
    width: 40px;
    height: 40px;
    font-size: 22px;
  }
  
  .loadingText,
  .errorText {
    font-size: 26px;
  }
  
  .emptyText {
    font-size: 28px;
  }
  
  .emptyTip {
    font-size: 22px;
  }
  
  .footer {
    margin: 16px;
    margin-bottom: 0;
    padding: 16px;
  }
  
  .footerTip {
    font-size: 22px;
  }
}

// 动画效果
.materialItem {
  animation: slideInUp 0.3s ease-out;
  animation-fill-mode: both;
}

.materialItem:nth-child(1) { animation-delay: 0.1s; }
.materialItem:nth-child(2) { animation-delay: 0.2s; }
.materialItem:nth-child(3) { animation-delay: 0.3s; }
.materialItem:nth-child(4) { animation-delay: 0.4s; }
.materialItem:nth-child(5) { animation-delay: 0.5s; }

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 主题色彩变体
.container.theme-blue {
  .indexNumber {
    background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
    box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  }
  
  .footer {
    border-left-color: #1890ff;
  }
}

.container.theme-green {
  .indexNumber {
    background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
    box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
  }
  
  .footer {
    border-left-color: #52c41a;
  }
}

.container.theme-purple {
  .indexNumber {
    background: linear-gradient(135deg, #722ed1 0%, #531dab 100%);
    box-shadow: 0 4px 12px rgba(114, 46, 209, 0.3);
  }
  
  .footer {
    border-left-color: #722ed1;
  }
}
