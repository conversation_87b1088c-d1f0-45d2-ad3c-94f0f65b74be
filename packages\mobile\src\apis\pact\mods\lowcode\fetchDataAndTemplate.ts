/**
 * @description 根据cityCode, mainId 和openId 获取最新的数据和模板
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityCode */
  cityCode: string;
  /** mainId */
  mainId: string;
  /** openId */
  openId: string;
}

export type Result = defs.pact.RespWrapper<defs.pact.ClcData>;
export const path = '/yc-wepact-mobile/clc/fetchDataAndTemplate';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
