/**
 * @description 取消预约接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.AppointmentCancelBean;
export const path = '/yc-wepact-mobile/appoint/cancelAppointment';
export const method = 'POST';
export const request = (
  data: defs.pact.AppointmentCancelParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.AppointmentCancelParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
