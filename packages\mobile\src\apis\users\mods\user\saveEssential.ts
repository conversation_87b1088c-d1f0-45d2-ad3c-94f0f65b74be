/**
 * @description saveEssential
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.users.ExecuteResult;
export const path = '/user-server/api/saveEssential';
export const method = 'POST';
export const request = (
  data: defs.users.EssentialInfo,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.users.EssentialInfo,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
