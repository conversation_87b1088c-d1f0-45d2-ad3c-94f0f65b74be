/**
 * @description rpa接口
 */
import * as checkToken from './checkToken';
import * as getAverSalary from './getAverSalary';
import * as getAverSalaryByYear from './getAverSalaryByYear';
import * as getFundBase from './getFundBase';
import * as getFundByMonth from './getFundByMonth';
import * as getLatestFund from './getLatestFund';
import * as getLatestFundCity from './getLatestFundCity';
import * as getLatestSalary from './getLatestSalary';
import * as getLatestSs from './getLatestSs';
import * as getLatestSsCity from './getLatestSsCity';
import * as getPersonCategory from './getPersonCategory';
import * as getSalaryByMonth from './getSalaryByMonth';
import * as getSsBase from './getSsBase';
import * as getSsByMonth from './getSsByMonth';
import * as getSsProcessList from './getSsProcessList';
import * as getStewardInfo from './getStewardInfo';

export {
  checkToken,
  getAverSalary,
  getAverSalaryByYear,
  getFundBase,
  getFundByMonth,
  getLatestFund,
  getLatestFundCity,
  getLatestSalary,
  getLatestSs,
  getLatestSsCity,
  getPersonCategory,
  getSalaryByMonth,
  getSsBase,
  getSsByMonth,
  getSsProcessList,
  getStewardInfo,
};
