/**
 * @description 获取材料
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.MaterialsPackage>;
export const path = '/wx-ncmp/policy/getMaterial';
export const method = 'POST';
export const request = (
  data: defs.ncmp.BusinessSubTypeMailQuery,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.BusinessSubTypeMailQuery,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
