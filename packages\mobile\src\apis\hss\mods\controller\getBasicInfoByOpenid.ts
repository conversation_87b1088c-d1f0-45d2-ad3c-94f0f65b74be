/**
 * @description 根据openid获取汇算基本信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openid */
  openid: string;
}

export type Result = defs.hss.GeneralRespBean<defs.hss.HsBasicInfo>;
export const path = '/yc-hs/api/getBasicInfoByOpenid/{openid}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
