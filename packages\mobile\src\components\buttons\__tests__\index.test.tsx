import React from 'react'
import { render, fireEvent } from '@testing-library/react'
import Buttons, { ButtonIconType, ButtonSize, ButtonType } from '../index'

describe('Buttons Component', () => {
  // 基本渲染测试
  it('should render with default props', () => {
    const { getByRole } = render(<Buttons />)
    const button = getByRole('button')
    expect(button).toBeInTheDocument()
  })

  it('should render with title', () => {
    const { getByText } = render(<Buttons title="Test Button" />)
    expect(getByText('Test Button')).toBeInTheDocument()
  })

  // 图标测试
  it('should render with detail icon by default', () => {
    const { container } = render(<Buttons />)
    const image = container.querySelector('image')
    expect(image).toBeInTheDocument()
  })

  it('should render with cancel icon', () => {
    const { container } = render(<Buttons icon={ButtonIconType.CANCEL} />)
    const button = container.querySelector('button')
    expect(button).toHaveClass('cancel_wrap')
  })

  it('should render with order icon', () => {
    const { container } = render(<Buttons icon={ButtonIconType.ORDER} />)
    const image = container.querySelector('image')
    expect(image).toHaveClass('order_icon')
  })

  // 尺寸测试
  it('should render with small size', () => {
    const { container } = render(<Buttons size={ButtonSize.SMALL} />)
    const button = container.querySelector('button')
    expect(button).toHaveClass('small')
  })

  it('should render with large size', () => {
    const { container } = render(<Buttons size={ButtonSize.LARGE} />)
    const button = container.querySelector('button')
    expect(button).toHaveClass('large')
  })

  // 状态测试
  it('should be disabled when disabled prop is true', () => {
    const { getByRole } = render(<Buttons disabled={true} />)
    const button = getByRole('button')
    expect(button).toBeDisabled()
  })

  it('should show loading text when loading', () => {
    const { getByText } = render(<Buttons loading={true} title="Submit" />)
    expect(getByText('加载中...')).toBeInTheDocument()
  })

  it('should have loading class when loading', () => {
    const { container } = render(<Buttons loading={true} />)
    const button = container.querySelector('button')
    expect(button).toHaveClass('loading')
  })

  // 点击事件测试
  it('should call onClick when clicked', () => {
    const handleClick = jest.fn()
    const { getByRole } = render(<Buttons onClick={handleClick} />)
    const button = getByRole('button')
    
    fireEvent.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('should not call onClick when disabled', () => {
    const handleClick = jest.fn()
    const { getByRole } = render(<Buttons onClick={handleClick} disabled={true} />)
    const button = getByRole('button')
    
    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  it('should not call onClick when loading', () => {
    const handleClick = jest.fn()
    const { getByRole } = render(<Buttons onClick={handleClick} loading={true} />)
    const button = getByRole('button')
    
    fireEvent.click(button)
    expect(handleClick).not.toHaveBeenCalled()
  })

  // 自定义样式测试
  it('should apply custom className', () => {
    const { container } = render(<Buttons className="custom-class" />)
    const button = container.querySelector('button')
    expect(button).toHaveClass('custom-class')
  })

  // 测试ID测试
  it('should apply testId', () => {
    const { getByTestId } = render(<Buttons testId="test-button" />)
    expect(getByTestId('test-button')).toBeInTheDocument()
  })

  // 边界情况测试
  it('should handle empty title gracefully', () => {
    const { container } = render(<Buttons title="" />)
    const text = container.querySelector('.text')
    expect(text).toHaveTextContent('')
  })

  it('should handle invalid icon gracefully', () => {
    const { container } = render(<Buttons icon="invalid-icon" />)
    const button = container.querySelector('button')
    // Should fallback to default wrap class
    expect(button).toHaveClass('wrap')
  })

  // 组合状态测试
  it('should handle multiple props correctly', () => {
    const handleClick = jest.fn()
    const { container, getByText } = render(
      <Buttons
        title="Complex Button"
        icon={ButtonIconType.CANCEL}
        size={ButtonSize.LARGE}
        className="custom-class"
        onClick={handleClick}
        testId="complex-button"
      />
    )

    const button = container.querySelector('button')
    expect(button).toHaveClass('cancel_wrap')
    expect(button).toHaveClass('large')
    expect(button).toHaveClass('custom-class')
    expect(getByText('Complex Button')).toBeInTheDocument()
    
    fireEvent.click(button)
    expect(handleClick).toHaveBeenCalledTimes(1)
  })
})
