class CtEbmBookingImageDTO {
  /** 预约ID */
  bookingId = undefined;

  /** 主键 */
  ebmBookingImageId = undefined;

  /** 自动生成的文件名 */
  fileName = '';

  /** url中的相对路径 */
  relPath = '';

  /** 临时uuid */
  uuid = '';
}

class CtEbmBusinessImageDTO {
  /** 业务办理id */
  businessId = undefined;

  /** 主键 */
  ebmBusinessImageId = undefined;

  /** 图片名 */
  fileName = '';

  /** 图片的对于webcontent的相对路径 */
  relPath = '';
}

class HttpResult {
  /** 返回状态code */
  code = '';

  /** 错误信息 */
  errorMsg = '';

  /** 具体的返回结果 */
  resultObj = new UploadResult();

  /** suc */
  suc = false;
}

class UploadMatFileParam {
  /** 用户账户id */
  accountId = '';

  /** 由于上传目前还支持更新，因此如果带了 bookImageId ，就是更新 */
  bookImageId = undefined;

  /** 同一个入职办理数据中文件的busUUID相同 */
  businessId = '';

  /** 城市代码 */
  cityCode = '';

  /** 文件名词 */
  fileName = '';

  /** 访问腾讯下载图片的地址的素材ID */
  mediaId = '';

  /** openId */
  openId = '';

  /** 文件类型 */
  type = '';
}

class UploadResult {
  /** 图片的主键 */
  bookImageId = undefined;

  /** 访问的url */
  url = '';

  /** 图片唯一名 */
  uuid = '';
}

export const upload = {
  CtEbmBookingImageDTO,
  CtEbmBusinessImageDTO,
  HttpResult,
  UploadMatFileParam,
  UploadResult,
};
