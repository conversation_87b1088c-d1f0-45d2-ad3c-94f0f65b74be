/**
 * @description User Controller
 */
import * as deleteChildrenEdu from './deleteChildrenEdu';
import * as deleteContinuingEdu from './deleteContinuingEdu';
import * as deleteEndowmentInsuranceInfo from './deleteEndowmentInsuranceInfo';
import * as deleteHealthInsuranceInfo from './deleteHealthInsuranceInfo';
import * as deleteHouseLoanInfo from './deleteHouseLoanInfo';
import * as deleteHouseRentalInfo from './deleteHouseRentalInfo';
import * as deleteMedicalInfo from './deleteMedicalInfo';
import * as deleteSupportInfo from './deleteSupportInfo';
import * as deleteTSupportInfo from './deleteTSupportInfo';
import * as getAccountInfo from './getAccountInfo';
import * as getCertificationInfo from './getCertificationInfo';
import * as getChildrenBasicInfo from './getChildrenBasicInfo';
import * as getChildrenEduInfo from './getChildrenEduInfo';
import * as getCityInfo from './getCityInfo';
import * as getContinuingBasicInfo from './getContinuingBasicInfo';
import * as getContinuingEduInfo from './getContinuingEduInfo';
import * as getCountryInfo from './getCountryInfo';
import * as getCustName from './getCustName';
import * as getEndowmentInsuranceInfo from './getEndowmentInsuranceInfo';
import * as getEssentialInfo from './getEssentialInfo';
import * as getEventFlag from './getEventFlag';
import * as getEventInfo from './getEventInfo';
import * as getHealthInsuranceInfo from './getHealthInsuranceInfo';
import * as getHouseBasicInfo from './getHouseBasicInfo';
import * as getHouseLoanInfo from './getHouseLoanInfo';
import * as getHouseRentalInfo from './getHouseRentalInfo';
import * as getLoanInfo from './getLoanInfo';
import * as getMedicalBasicInfo from './getMedicalBasicInfo';
import * as getMedicalInfo from './getMedicalInfo';
import * as getSupportInfo from './getSupportInfo';
import * as getTSupportInfo from './getTSupportInfo';
import * as isTaxApply from './isTaxApply';
import * as saveChildrenEdu from './saveChildrenEdu';
import * as saveContinuingEdu from './saveContinuingEdu';
import * as saveEndowmentInsuranceInfo from './saveEndowmentInsuranceInfo';
import * as saveEssential from './saveEssential';
import * as saveHealthInsuranceInfo from './saveHealthInsuranceInfo';
import * as saveHouseLoanInfo from './saveHouseLoanInfo';
import * as saveHouseRentalInfo from './saveHouseRentalInfo';
import * as saveMedicalInfo from './saveMedicalInfo';
import * as saveSupportInfo from './saveSupportInfo';
import * as saveTSupportInfo from './saveTSupportInfo';

export {
  deleteChildrenEdu,
  deleteContinuingEdu,
  deleteEndowmentInsuranceInfo,
  deleteHealthInsuranceInfo,
  deleteHouseLoanInfo,
  deleteHouseRentalInfo,
  deleteMedicalInfo,
  deleteSupportInfo,
  deleteTSupportInfo,
  getAccountInfo,
  getCertificationInfo,
  getChildrenBasicInfo,
  getChildrenEduInfo,
  getCityInfo,
  getContinuingBasicInfo,
  getContinuingEduInfo,
  getCountryInfo,
  getCustName,
  getEndowmentInsuranceInfo,
  getEssentialInfo,
  getEventFlag,
  getEventInfo,
  getHealthInsuranceInfo,
  getHouseBasicInfo,
  getHouseLoanInfo,
  getHouseRentalInfo,
  getLoanInfo,
  getMedicalBasicInfo,
  getMedicalInfo,
  getSupportInfo,
  getTSupportInfo,
  isTaxApply,
  saveChildrenEdu,
  saveContinuingEdu,
  saveEndowmentInsuranceInfo,
  saveEssential,
  saveHealthInsuranceInfo,
  saveHouseLoanInfo,
  saveHouseRentalInfo,
  saveMedicalInfo,
  saveSupportInfo,
  saveTSupportInfo,
};
