class AcCheckLog {
  /** 外勤签到地址（实际打卡） */
  checkAdd = '';

  /** 打卡记录id */
  checkLogId = undefined;

  /** 外勤签到备注,补签备注 */
  checkRemark = '';

  /** 打卡时间 */
  checkTime = '';

  /** 打卡类型（1上班 2下班 3外勤） */
  checkType = undefined;

  /** 创建者 */
  createBy = undefined;

  /** 创建时间 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 数据来源(1正常打卡2补签3导入) */
  dataSource = undefined;

  /** 部门id */
  deptId = undefined;

  /** 设备型号 */
  deviceDes = '';

  /** 设备id字符 */
  deviceId = '';

  /** 员工id */
  eosEmpId = undefined;

  /** 证件号码 */
  idCardNum = '';

  /** 证件类型 */
  idCardType = undefined;

  /** 删除标志（0代表存在 2代表删除） */
  isDeleted = undefined;

  /** 纬（实际打卡） */
  latitude = '';

  /** 办公地址id */
  locSetId = undefined;

  /** 打卡地点描述 */
  locSetName = '';

  /** 经度（实际打卡） */
  longitude = '';

  /** 外勤签到图片url */
  picUrl = '';

  /** 备注 */
  remark = '';

  /** 更新者 */
  updateBy = undefined;

  /** 更新时间 */
  updateDt = '';
}

class AcHolApply {
  /** 申请id */
  applyId = undefined;

  /** 申请时间 */
  applyTime = '';

  /** 孩子出生日期 */
  childBirthday = '';

  /** 婴儿数量 */
  childNumber = undefined;

  /** 孩子预产期 */
  childPreborn = '';

  /** 创建人 */
  createBy = '';

  /** 创建时间 */
  createDt = '';

  /** 客户id */
  custId = undefined;

  /** 部门id */
  deptId = undefined;

  /** 结束日期 */
  endTime = '';

  /** 员工id */
  eosEmpId = undefined;

  /** 审批结束时间 */
  examOverTime = '';

  /** 审批是否结束（0未结束/1已结束） */
  examStepIsOver = undefined;

  /** 请假折合的天数 */
  holDays = undefined;

  /** 请假时数 */
  holHours = undefined;

  /** 请假类型id */
  holId = undefined;

  /** 请假名称 */
  holName = '';

  /** 是否删除（0否 1是） */
  isDeleted = undefined;

  /** length */
  length = undefined;

  /** marriageRegistrationDay */
  marriageRegistrationDay = '';

  /** 备注 */
  remark = '';

  /** 流水号 */
  serialNum = '';

  /** 副本版本号 */
  sonVer = undefined;

  /** start */
  start = undefined;

  /** 开始日期 */
  startTime = '';

  /** 审批状态（1审批通过 2待审批 3驳回 4撤销） */
  state = undefined;

  /** 更新人 */
  updateBy = '';

  /** 更新时间 */
  updateDt = '';

  /** 显示时长 */
  viewHours = '';
}

class AveSalary {
  /** 月度平均工资 */
  avgPayMon = '';

  /** 年度平均工资 */
  avgPayYear = '';

  /** 执行时间 */
  execStartTime = '';

  /** 最低工资（小时）分6档填入 1档 */
  minHour1 = '';

  /** minHour2 */
  minHour2 = '';

  /** minHour3 */
  minHour3 = '';

  /** minHour4 */
  minHour4 = '';

  /** minHour5 */
  minHour5 = '';

  /** minHour6 */
  minHour6 = '';

  /** 最低工资（小时）分6档填入 1档适用区县 */
  minHourCounty1 = '';

  /** minHourCounty2 */
  minHourCounty2 = '';

  /** minHourCounty3 */
  minHourCounty3 = '';

  /** minHourCounty4 */
  minHourCounty4 = '';

  /** minHourCounty5 */
  minHourCounty5 = '';

  /** minHourCounty6 */
  minHourCounty6 = '';

  /** 最低工资（月度）分6档填入，1档 */
  minMon1 = '';

  /** minMon2 */
  minMon2 = '';

  /** minMon3 */
  minMon3 = '';

  /** minMon4 */
  minMon4 = '';

  /** minMon5 */
  minMon5 = '';

  /** minMon6 */
  minMon6 = '';

  /** 最低工资（月度）分6档填入，1档 适用区县 */
  minMonCounty1 = '';

  /** minMonCounty2 */
  minMonCounty2 = '';

  /** minMonCounty3 */
  minMonCounty3 = '';

  /** minMonCounty4 */
  minMonCounty4 = '';

  /** minMonCounty5 */
  minMonCounty5 = '';

  /** minMonCounty6 */
  minMonCounty6 = '';

  /** 省份/城市 */
  pcName = '';

  /** 所属年份 */
  yearDate = '';
}

class AveSalaryStr {
  /** str1 */
  str1 = '';

  /** str2 */
  str2 = '';

  /** str3 */
  str3 = '';
}

class BusNameClass {
  /** 业务项目id */
  bussNameClassId = '';

  /** 业务项目名称 */
  bussNameClassName = '';
}

class BusinessSubType {
  /** 小类id */
  busSubtypeId = '';

  /** 小类名称 */
  busSubtypeName = '';

  /** 大类id */
  busTypeId = '';

  /** 大类名称 */
  busTypeName = '';

  /** 业务项目id */
  bussNameClassId = '';

  /** 业务项目名称 */
  bussNameClassName = '';

  /** 所属类型 */
  categoryId = '';

  /** 雇员id */
  empId = '';

  /** 是否可预约 */
  isBooked = '';

  /** 个人空表模板 */
  pBlankTemplatePath = '';

  /** 个人空表模板名 */
  pBlankTemplatePathName = '';

  /** 个人样本模板 */
  pSampleTemplatePath = '';

  /** 个人样本模板名 */
  pSampleTemplatePathName = '';

  /** 业务说明 */
  remark = '';
}

class BusinessSubTypeMailQuery {
  /** accountId */
  accountId = '';

  /** busSubtypeIds */
  busSubtypeIds = '';

  /** emailAddress */
  emailAddress = '';

  /** openId */
  openId = '';
}

class BusinessType {
  /** 大类id */
  busTypeId = '';

  /** 大类名称 */
  busTypeName = '';

  /** 业务项目名称id */
  busnameClassId = '';

  /** 所属类型 */
  categoryId = '';

  /** 城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';
}

class City {
  /** cityId */
  cityId = '';

  /** cityName */
  cityName = '';
}

class EcBusiness {
  /** 业务Id */
  businessId = '';

  /** 城市码 */
  cityCode = '';

  /** 电子业务Id */
  eleBusinessId = '';

  /** 电子业务名称 */
  eleBusinessName = '';

  /** 电子签署标志(1未发起、2拟定中、3员工已签署、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废、11客户已签署、12员工已作废) */
  eleBusinessStatus = undefined;

  /** 电子业务状态名称 */
  eleBusinessStatusName = '';

  /** 上下岗id */
  empHireSepId = '';

  /** 员工id */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 手机号码 */
  phoneNumber = '';
}

class EcQuit {
  /** 离职证明电子合同id */
  certificateEleId = '';

  /** 离职证明电子合同名称 */
  certificateElename = '';

  /** 电子离职证明状态:1未发起、5已完成、6已过期、9已作废 */
  certificateStatus = undefined;

  /** 电子离职证明状态名称:1未发起、5已完成、6已过期、9已作废 */
  certificateStatusName = '';

  /** 签署URL */
  eleSinUrl = '';

  /** 上下岗id */
  empHireSepId = '';

  /** empId */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 离职材料电子合同id */
  materialEleId = '';

  /** 离职材料电子合同名称 */
  materialEleName = '';

  /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
  materialStatus = undefined;

  /** 电子离职材料状态:1未发起、2拟定中、3签署中、4作废中、5已完成、6已过期、7已撤回、9已作废、10 已发起作废  */
  materialStatusName = '';

  /** 手机号码 */
  phoneNumber = '';

  /** 离职任务编号 */
  quitTaskId = undefined;
}

class EleContract {
  /** 城市码 */
  cityCode = '';

  /** 电子合同Id */
  eleContractId = '';

  /** 电子合同名称 */
  eleContractName = '';

  /** 电子合同状态 */
  eleContractStatus = '';

  /** 签署URL */
  eleSinUrl = '';

  /** 雇员Id */
  empId = '';

  /** 姓名 */
  empName = '';

  /** 身份证号码 */
  idCardNum = '';

  /** 劳动合同id */
  laborContractId = '';

  /** 手机号码 */
  phoneNumber = '';
}

class ElecSignResult {
  /** 电子签链接 */
  eleSinUrl = '';

  /** 管家地址 */
  stewardAddr = '';

  /** 管家姓名 */
  stewardName = '';

  /** 管家联系方式 */
  stewardTel = '';

  /** 管家油箱 */
  stewardmail = '';
}

class EmpFeeCity {
  /** 城市名称 */
  cityName = '';

  /** 客户名称 */
  custName = '';
}

class EmpFeeProcess {
  /** 产品名称列表 */
  products = '';

  /** 参保状态 */
  statusWx = '';
}

class FringeBenefitsQuery {
  /** busnameSubtypeId */
  busnameSubtypeId = '';

  /** categoryId */
  categoryId = '';
}

class FringeBenefitsVO {
  /** accordingFileNumber1 */
  accordingFileNumber1 = '';

  /** accordingFileNumber2 */
  accordingFileNumber2 = '';

  /** accordingFileNumber3 */
  accordingFileNumber3 = '';

  /** benefitsId */
  benefitsId = undefined;

  /** busnameClassId */
  busnameClassId = undefined;

  /** busnameClassName */
  busnameClassName = '';

  /** busnameSubtypeId */
  busnameSubtypeId = undefined;

  /** busnameSubtypeName */
  busnameSubtypeName = '';

  /** busnameTypeId */
  busnameTypeId = undefined;

  /** busnameTypeName */
  busnameTypeName = '';

  /** categoryId */
  categoryId = undefined;

  /** categoryName */
  categoryName = '';

  /** cityId */
  cityId = undefined;

  /** cityName */
  cityName = '';

  /** createBy */
  createBy = undefined;

  /** createDt */
  createDt = '';

  /** crossCityHandle */
  crossCityHandle = undefined;

  /** crossCityHandleArea */
  crossCityHandleArea = '';

  /** crossCityHandleText */
  crossCityHandleText = '';

  /** crossProvinceHandle */
  crossProvinceHandle = undefined;

  /** crossProvinceHandleArea */
  crossProvinceHandleArea = '';

  /** crossProvinceHandleText */
  crossProvinceHandleText = '';

  /** crossRegionHandle */
  crossRegionHandle = undefined;

  /** crossRegionHandleArea */
  crossRegionHandleArea = '';

  /** crossRegionHandleText */
  crossRegionHandleText = '';

  /** eHandleCondition */
  eHandleCondition = '';

  /** eHandleOfflineProcess */
  eHandleOfflineProcess = '';

  /** eHandleProcess1 */
  eHandleProcess1 = '';

  /** eHandleProcess2 */
  eHandleProcess2 = '';

  /** eHandleProcess3 */
  eHandleProcess3 = '';

  /** effectiveDate1 */
  effectiveDate1 = '';

  /** effectiveDate2 */
  effectiveDate2 = '';

  /** effectiveDate3 */
  effectiveDate3 = '';

  /** fileName1 */
  fileName1 = '';

  /** fileName2 */
  fileName2 = '';

  /** fileName3 */
  fileName3 = '';

  /** handleForm */
  handleForm = '';

  /** handleType */
  handleType = undefined;

  /** handleTypeText */
  handleTypeText = '';

  /** handleWindow1 */
  handleWindow1 = '';

  /** handleWindow2 */
  handleWindow2 = '';

  /** handleWindow3 */
  handleWindow3 = '';

  /** isBooked */
  isBooked = '';

  /** isDeleted */
  isDeleted = undefined;

  /** isValid */
  isValid = '';

  /** makReservations */
  makReservations = undefined;

  /** makReservationsText */
  makReservationsText = '';

  /** orderStatus */
  orderStatus = undefined;

  /** orderStatusText */
  orderStatusText = '';

  /** otherHandleInfo1 */
  otherHandleInfo1 = '';

  /** otherHandleInfo2 */
  otherHandleInfo2 = '';

  /** otherHandleInfo3 */
  otherHandleInfo3 = '';

  /** otherHandleInfo4 */
  otherHandleInfo4 = '';

  /** otherHandleInfo5 */
  otherHandleInfo5 = '';

  /** otherHandleInfo6 */
  otherHandleInfo6 = '';

  /** otherPolicyInfo1 */
  otherPolicyInfo1 = '';

  /** otherPolicyInfo2 */
  otherPolicyInfo2 = '';

  /** otherPolicyInfo3 */
  otherPolicyInfo3 = '';

  /** otherPolicyInfo4 */
  otherPolicyInfo4 = '';

  /** otherPolicyInfo5 */
  otherPolicyInfo5 = '';

  /** otherPolicyInfo6 */
  otherPolicyInfo6 = '';

  /** otherPolicyInfo7 */
  otherPolicyInfo7 = '';

  /** otherPolicyInfo8 */
  otherPolicyInfo8 = '';

  /** otherPolicyInfo9 */
  otherPolicyInfo9 = '';

  /** pHandleCondition */
  pHandleCondition = '';

  /** pHandleOfflineProcess */
  pHandleOfflineProcess = '';

  /** pHandleProcess1 */
  pHandleProcess1 = '';

  /** pHandleProcess2 */
  pHandleProcess2 = '';

  /** pHandleProcess3 */
  pHandleProcess3 = '';

  /** payee */
  payee = '';

  /** personCategoryId */
  personCategoryId = undefined;

  /** personCategoryName */
  personCategoryName = '';

  /** policyFileId1 */
  policyFileId1 = '';

  /** policyFileId2 */
  policyFileId2 = '';

  /** policyFileId3 */
  policyFileId3 = '';

  /** policyFileName1 */
  policyFileName1 = '';

  /** policyFileName2 */
  policyFileName2 = '';

  /** policyFileName3 */
  policyFileName3 = '';

  /** policySource1 */
  policySource1 = '';

  /** policySource2 */
  policySource2 = '';

  /** policySource3 */
  policySource3 = '';

  /** policyUrl1 */
  policyUrl1 = '';

  /** policyUrl2 */
  policyUrl2 = '';

  /** policyUrl3 */
  policyUrl3 = '';

  /** processDifference */
  processDifference = '';

  /** ssStatus */
  ssStatus = undefined;

  /** ssStatusText */
  ssStatusText = '';

  /** statutoryDeadline */
  statutoryDeadline = '';

  /** supplementaryInfo1 */
  supplementaryInfo1 = '';

  /** supplementaryInfo2 */
  supplementaryInfo2 = '';

  /** supplementaryInfo3 */
  supplementaryInfo3 = '';

  /** supplementaryInfo4 */
  supplementaryInfo4 = '';

  /** supplementaryInfo5 */
  supplementaryInfo5 = '';

  /** supplementaryInfo6 */
  supplementaryInfo6 = '';

  /** termsContent1 */
  termsContent1 = '';

  /** termsContent2 */
  termsContent2 = '';

  /** termsContent3 */
  termsContent3 = '';

  /** tollHandle */
  tollHandle = undefined;

  /** tollHandleText */
  tollHandleText = '';

  /** tollStandard */
  tollStandard = '';

  /** updateBy */
  updateBy = undefined;

  /** updateDt */
  updateDt = '';

  /** windowAddress */
  windowAddress = '';
}

class HttpResult {
  /** 返回状态code */
  code = '';

  /** 错误信息 */
  errorMsg = '';

  /** 具体的返回结果 */
  resultObj = new BusNameClass();

  /** suc */
  suc = false;
}

class HttpRpaResult {
  /** 具体返回内容 */
  msg_response = new InnerResponse();

  /** Web API调用成功或失败 ， success->成功 ， 其他->失败 */
  parse_result = '';
}

class HzSupplyCert {
  /** 审批状态 */
  auditStatus = '';

  /** 证件id */
  certId = '';

  /** 证件名称 */
  certName = '';

  /** 文件名称 */
  fileName = '';

  /** 文件路径 */
  filePath = '';

  /** 是否必填 1是 0否 */
  isMust = '';

  /** 上传文件id */
  mediaId = '';

  /** openId */
  openId = '';

  /** 驳回原因 */
  rejectReason = '';

  /** 主键 */
  supplyCertId = '';

  /** 批次id */
  supplyId = '';

  /** 上传方 1客服 2个人 */
  uploadType = '';
}

class InnerResponse {
  /** 移出场景内的变量 */
  remove = new AveSalaryStr();

  /** 存进场景内的变量 */
  update = new AveSalaryStr();
}

class Map {}

class MaterialsPackage {
  /** 小类id */
  busSubtypeId = '';

  /** 大类id */
  busTypeId = '';

  /** 是否是单位办理材料1 是 0否 */
  isEMaterial = '';

  /** 是否原件 */
  isOriginal = '';

  /** 是否是个人办理材料1 是 0否 */
  isPMaterial = '';

  /** 是否返还给申请人 */
  isReturn = '';

  /** 材料数量 */
  materialsAccount = '';

  /** 材料编号 */
  materialsId = '';

  /** 材料名称 */
  materialsName = '';

  /** 材料包id */
  packageId = '';
}

class PersonCategory {
  /** 人员分类id */
  personCategoryId = '';

  /** 人员分类名称 */
  personCategoryName = '';
}

class PolicyDetail {
  /** 生效日期 */
  effectiveDate = '';

  /** 失效日期 */
  expirationDate = '';

  /** 所属年份 */
  policyTemplateInfoYear = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** templateGroupRespList */
  templateGroupRespList = [];

  /** 适用范围:1国家 2省份3城市4特区 */
  templateScope = undefined;
}

class PolicyField {
  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段编码 */
  fieldCode = '';

  /** 字段名称 */
  fieldName = '';

  /** 字段值 */
  fieldValue = '';

  /** 附件名称 */
  fileName = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 字段是否必填 0否 1是 */
  isMust = undefined;

  /** 字段选项 */
  items = '';

  /** 政策模板字段ID */
  policyTemplateFieldId = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** 字段类型:1.文本 2.多行文本 3.日期 4.下拉菜单 5.链接 6.附件 7.多选项 8.数字(整数) 9.数字(小数) */
  type = undefined;

  /** 超链接名称 */
  urlName = '';

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class PolicyGroup {
  /** 客户端是否显示 0：不显示,1：显示 */
  clientShowState = undefined;

  /** 字段列表 */
  fields = [];

  /** 分组编号 */
  groupCode = '';

  /** HRO端是否显示 0：不显示,1：显示 */
  hroShowState = undefined;

  /** 是否删除 0否 1是 */
  isDeleted = '';

  /** 政策模板分组ID */
  policyTemplateGroupId = '';

  /** 政策模板ID */
  policyTemplateId = '';

  /** 显示顺序 */
  seqNum = undefined;

  /** 分组名称 */
  templateGroupName = '';

  /** 模板状态  状态:0初始 1有效 2无效 */
  templateState = undefined;

  /** wx端是否显示 0：不显示,1：显示 */
  wxShowState = undefined;
}

class PolicyLevelTree {
  /** children */
  children = [];

  /** id */
  id = '';

  /** name */
  name = '';

  /** pId */
  pId = '';
}

class PrivacyParam {
  /** openId */
  openId = '';

  /** 隐私协议当前最新版本id */
  privacyId = undefined;

  /** 隐私协议当前最新版本号 */
  version = undefined;
}

class PrivacyRegisterDTO {
  /** 隐私协议当前最新版本的内容,只有当用户没签最新协议时才返回 */
  content = '';

  /** 隐私协议当前最新版本id,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
  privacyId = undefined;

  /** 隐私协议登记记录主表id,只有当用户已经签过了最新协议后才返回 */
  privacyRegisterId = undefined;

  /** registed */
  registed = false;

  /** 隐私协议当前最新版本号,如果返回-1，说明压根没有维护的记录，这时候不能让他同意 */
  version = undefined;
}

class QueryPolicyEncyclopedia {
  /** 城市id集合 */
  cityIds = [];

  /** endIndex */
  endIndex = undefined;

  /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
  infoState = undefined;

  /** 政策生效状态 0初始 1待生效 2 生效 3失效 */
  infoStates = [];

  /** 是否全部城市:0否，1是 */
  isAllCity = undefined;

  /** 是否全部省份:0否，1是 */
  isAllProvince = undefined;

  /** 是否全部特区i:0否，1是 */
  isAllSpecialArea = undefined;

  /** 一级名称Id */
  level1Id = '';

  /** 一级名称 */
  level1Name = '';

  /** 二级名称Id */
  level2Id = '';

  /** 二级名称 */
  level2Name = '';

  /** 是否分页查询（true:分页；false:不分页） */
  pageQuery = false;

  /** 政策模板详情ID */
  policyTemplateInfoId = undefined;

  /** 所属年份 */
  policyTemplateInfoYear = '';

  /** 所属年份 */
  policyTemplateInfoYears = [];

  /** 政策标题Id */
  policyTitleId = '';

  /** 省份城市 */
  provinceCity = '';

  /** 省份id集合 */
  provinceIds = [];

  /** 适用范围:1国家 2省份3城市4特区 */
  queryScopes = [];

  /** 服务类型 */
  serviceType = undefined;

  /** 服务类型名称 */
  serviceTypeName = '';

  /** 服务类型集合 */
  serviceTypes = [];

  /** 特区id集合 */
  specialAreaIds = [];

  /** startIndex */
  startIndex = undefined;

  /** 政策详情名称 */
  templateInfoName = '';

  /** 适用范围:1国家 2省份3城市 */
  templateScope = '';
}

class RpaEmpFee {
  /** 合计金额 */
  amt = '';

  /** 没权限提示文本信息 */
  authorityHintMsg = '';

  /** 社保组类型 1社保 2公积金 */
  category = '';

  /** 客户名称 */
  custName = '';

  /** 企业金额 */
  eAmt = '';

  /** empFeeProcessList */
  empFeeProcessList = [];

  /** 雇员id */
  empId = '';

  /** 是否授权 1是 0否 */
  ifAuthority = false;

  /** 是否有数据 1是 0否 */
  ifHasData = false;

  /** 个人金额 */
  pAmt = '';

  /** 参保项目与状态 */
  productStatusName = '';

  /** 服务月 */
  serviceMonth = '';
}

class Salary {
  /** 没权限提示文本信息 */
  authorityHintMsg = '';

  /** 客户名称 */
  custName = '';

  /** empId */
  empId = '';

  /** 收入合计 */
  f1 = '';

  /** 本次扣税 */
  f10 = '';

  /** 扣款合计 */
  f2 = '';

  /** 实发合计 */
  f3 = '';

  /** 是否授权 true是 false否 */
  ifAuthority = false;

  /** 是否有数据 true是 false否 */
  ifHasData = false;

  /** sendId */
  sendId = '';

  /** sendMonth */
  sendMonth = '';

  /** 发放状态 */
  wageBatchStatusName = '';

  /** 报税状态 */
  wageTaxStatus = '';
}

class SsProcess {
  /** 办理时间 */
  createdt = '';

  /** 社保组 */
  groupName = '';

  /** 社保组产品 */
  products = '';

  /** 办理状态 */
  statusWx = '';
}

class SsProcessDto {
  /** empId */
  empId = '';

  /** 1 社保，2 公积金 */
  groupType = '';
}

class StewardInfo {
  /** 地址 */
  address = '';

  /** 联系方式 */
  contact = '';

  /** 雇员id（不用） */
  empId = '';

  /** 邮箱 */
  mail = '';

  /** 供应商类型（不用） */
  providerType = '';

  /** 管家姓名 */
  stewardName = '';
}

class TokenCheck {
  /** 当前用户类型 1:认证用户 2:匿名用户 */
  userType = undefined;
}

class WechatHiresepDTO {
  /** 注册账号对应的ID */
  accountId = '';

  /** 办理城市id */
  cityId = '';

  /** 城市名称 */
  cityName = '';

  /** 微信入职办理主表id */
  empHiresepMainId = '';

  /** 低代码表单的json数据 */
  jsonStr = '';

  /** 关联照片信息的uuid */
  uuid = '';
}

export const ncmp = {
  AcCheckLog,
  AcHolApply,
  AveSalary,
  AveSalaryStr,
  BusNameClass,
  BusinessSubType,
  BusinessSubTypeMailQuery,
  BusinessType,
  City,
  EcBusiness,
  EcQuit,
  EleContract,
  ElecSignResult,
  EmpFeeCity,
  EmpFeeProcess,
  FringeBenefitsQuery,
  FringeBenefitsVO,
  HttpResult,
  HttpRpaResult,
  HzSupplyCert,
  InnerResponse,
  Map,
  MaterialsPackage,
  PersonCategory,
  PolicyDetail,
  PolicyField,
  PolicyGroup,
  PolicyLevelTree,
  PrivacyParam,
  PrivacyRegisterDTO,
  QueryPolicyEncyclopedia,
  RpaEmpFee,
  Salary,
  SsProcess,
  SsProcessDto,
  StewardInfo,
  TokenCheck,
  WechatHiresepDTO,
};
