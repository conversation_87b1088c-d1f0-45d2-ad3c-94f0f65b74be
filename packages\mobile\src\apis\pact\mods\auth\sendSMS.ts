/**
 * @description 绑定获取验证码
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.pact.SmsResponseBean;
export const path = '/yc-wepact-mobile/auth/sendSMS';
export const method = 'POST';
export const request = (
  data: defs.pact.SmsParam,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.pact.SmsParam,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
