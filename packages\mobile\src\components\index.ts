/**
 * 组件内根据平台require导致小程序编译失败
 * 小程序不支持运行时require 等待后续支持吧
 */
import { useForm as useFormTem } from 'react-hook-form'

export { default as StatusBar } from './status-bar'
export { default as BoxShadow } from './box-shadow'
export { default as SafeAreaView } from './safe-area-view'
export { default as LinearGradient } from './linear-gradient'
export { default as KeyboardAwareScrollView } from './keyboard-aware-scroll-view'
export { default as Modal } from './modal'
export { default as Labels } from './labels'
export { default as Buttons } from './buttons'
export { default as Pciker } from './picker'
export { default as ImagePick } from './image-pick'
export { TimePicker } from './time-picker'
export {
  // 同vue员工预约办理页面
  DateSelect,
  // 同vue个人社保查询页面
  DateYearMonth,
  // 同vue工资及完税情况查询页面
  DateYearMonthWage,
  // 同vue薪资档案查询
  DateYearMonthSalary
} from './time-picker/page-date'
export { DragUpdataPage } from './drag-updata-page'
export { Noticebar } from './notice-bar'

export { default as Form } from './form'
export { default as FormTip } from './form/form-tip'
export { default as FormLabel } from './form/form-label'
export * from './form/type'

export const useForm: typeof useFormTem = p => useFormTem({ mode: 'all', ...p })

export { default as BottomBtn } from './bottom-btn'
export { withPage } from './page'

export { default as ListView } from './list-view/ListViewHoc'
export { usePagination } from './list-view/hooks/usePagination'
