import * as appBind from './appBind';
import * as appoint from './appoint';
import * as auth from './auth';
import * as busi from './busi';
import * as cityDynamicsForm from './cityDynamicsForm';
import * as cityInfo from './cityInfo';
import * as entry from './entry';
import * as login from './login';
import * as lowcode from './lowcode';
import * as per from './per';
import * as policy from './policy';
import * as templateMessage from './templateMessage';
import * as test from './test';
import * as wxConfig from './wxConfig';

export const pact = {
  appBind,
  appoint,
  auth,
  busi,
  cityDynamicsForm,
  cityInfo,
  entry,
  login,
  lowcode,
  per,
  policy,
  templateMessage,
  test,
  wxConfig,
};
