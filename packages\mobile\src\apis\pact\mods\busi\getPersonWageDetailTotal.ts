/**
 * @description 工资详情合计接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** empId */
  empId?: string;
  /** openId */
  openId?: string;
  /** sendMonth */
  sendMonth?: string;
}

export type Result = defs.pact.WageDetailTotalRespBean;
export const path = '/yc-wepact-mobile/busi/getPersonWageDetailTotal';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
