/**
 * @description 查询汇算城市
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityName */
  cityName: string;
  /** openid */
  openid: string;
}

export type Result = defs.hss.GeneralRespBean<Array<ObjectMap<string, string>>>;
export const path = '/yc-hs/hsInfo/getHsCity/{openid}/{cityName}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
