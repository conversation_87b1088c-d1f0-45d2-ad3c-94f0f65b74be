/**
 * @description 获取短信验证码 - 个人
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/api/sendSMSPersonal';
export const method = 'POST';
export const request = (data: object, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: object,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
