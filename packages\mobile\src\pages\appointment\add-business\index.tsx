import { Fragment, useEffect, useState, useRef } from 'react'
import { View, Text } from '@tarojs/components'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { Form, BottomBtn, FormItemProps, withPage, useForm, FormTip, FormLabel, MaterialList } from '@components'
import { getScrollStyle } from '@utils/transforms'
import { ncmp } from '@apis/ncmp'
import { pact } from '@apis/pact'
import { getGlobalData, getGuid, navigateTo } from '@utils'
import dayjs from 'dayjs'
import styles from './index.module.scss'

type AccountInfo = { idCardNum?: string; mobilePhoneNum?: string; empName?: string }
type options = { key: string; value: string }[]
const Options: options = [
  { key: '1', value: '社保业务' },
  { key: '2', value: '公积金业务' },
  { key: '3', value: '人力资源收费服务' },
  { key: '4', value: '定点医疗机构变更' }
]
const Index = () => {
  const [company, setCompany] = useState<defs.pact.CompanyResponseData>()
  const [things, setThings] = useState<any[]>()
  const [busTypes, setBusTypes] = useState<any[]>()
  const [accountInfo, setAccountInfo] = useState<AccountInfo>()
  const [materials, setMaterials] = useState<any[]>([])
  const companyOptionsRef = useRef<options>([])
  const { openId, accountId, empId } = getGlobalData<'account'>('account')
  const { categoryName, businessName } = useRouter().params
  const [uuid] = useState(getGuid())
  const scrollStyle = getScrollStyle({ bottom: 120 })
  const form = useForm()
  const onSubmit = (values: any) => {
    values.accountInfo.mobilePhoneNum = values?.mobilePhoneNum
    pact.appoint.appointment
      .request({
        ...values,
        busSubTypeIdStr: values?.busSubtypeId,
        cancelReason: '',
        uuid,
        busSubTypeIdStrName: undefined,
        createBy: empId,
        bookingRemark: values.bookingRemark?.replace(/[\r\n]/g, ''),
        openId
      })
      .then(res => {
        if (res.code == '200') {
          Taro.navigateBack()
          navigateTo('/appointment/success')
        } else {
          Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
        }
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  }
  useDidShow(() => {
    pact.per.personInformation
      .request({ accountId, openId })
      .then(res => {
        const { idCardNum, mobilePhoneNum, empName } = res.data || {}
        const _accountInfo = { idCardNum, mobilePhoneNum, empName }
        setAccountInfo(_accountInfo)
        form.setValue('accountInfo', _accountInfo)
        form.setValue('mobilePhoneNum', mobilePhoneNum)
      })
      .catch(() => {
        Taro.showToast({ icon: 'none', title: '系统异常,请稍后重试！' })
      })
  })
  const cityId = form.watch('cityId')
  const categoryId = form.watch('categoryId')
  const bussNameClassId = form.watch('bussNameClassId')

  const columns: FormItemProps[] = [
    {
      showLine: false,
      render: () => <FormLabel level={2} title='业务信息' />
    },
    {
      name: 'cityId',
      type: 'page_choice',
      title: '城市',
      rules: { required: true },
      pageOptions: {
        keys: ['cityId', 'cityName'],
        labelKey: 'cityName',
        url: '/policy-city'
      }
    },
    {
      isHidden: !company?.name,
      render: () => <View className={styles.company_name}>{company?.name}</View>
    },
    {
      isHidden: !company?.address,
      render: () => (
        <View>
          <View className={styles.company_title}>分公司联系信息</View>
          <View className={styles.company_name}>公司地址：{company?.address}</View>
          <View className={styles.company_name}>联系电话：{company?.contactTel}</View>
        </View>
      )
    },
    {
      title: '业务类型',
      name: 'categoryId',
      type: 'select',
      rules: { required: true },
      options: companyOptionsRef.current,
      disabled: !cityId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
      }
    },
    {
      title: '业务项目',
      name: 'busnameClassId',
      type: 'select',
      rules: { required: true },
      options: things,
      disabled: !cityId || !categoryId,
      onClick: () => {
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
        }
      }
    },
    {
      title: '业务内容',
      name: 'busContent',
      type: 'select',
      rules: { required: true },
      options: busTypes,
      disabled: !cityId || !categoryId || !bussNameClassId || busTypes?.length === 0,
      onClick: () => {
        if (busTypes?.length === 0) {
          Taro.showToast({ title: '暂无可选信息！' })
          return
        }
        if (!cityId) {
          Taro.showToast({ title: '请选择城市' })
          return
        }
        if (!categoryId) {
          Taro.showToast({ title: '请选择所属类型' })
          return
        }
        if (!bussNameClassId) {
          Taro.showToast({ title: '请选择业务项目' })
          return
        }
      }
    },
    {
      title: '预约人姓名',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.empName}</Text>
    },
    {
      title: '身份证号',
      rules: { required: true },
      render: () => <Text className={styles.company_name}>{accountInfo?.idCardNum}</Text>
    },
    {
      title: '手机号码',
      type: 'mobile',
      rules: { required: true },
      name: 'mobilePhoneNum'
    },
    {
      showLine: false,
      render: () => <FormLabel level={2} title='材料列表' />
    },
    {
      showLine: false,
    //   render: () => (
    //     <MaterialList
    //       busSubtypeId={form.watch('busContent')}
    //       businessId={uuid}
    //       onMaterialChange={setMaterials}
    //     />
    //   )
    },
    {
      // showLine: false,
      render: () => <FormTip tip=' 提示: 上述材料仅供参考，实际提交材料以易才管家推送为准。' />
    },
    {
      title: '员工自助办理途径',
      type: 'textarea'
    },
    {
      title: '是否自助办理',
      type: 'select'
    },
    {
      title: '预约备注',
      type: 'textarea',
      name: 'bookingRemark'
    }
  ]
  return (
    <Fragment>
      <Form style={scrollStyle} form={form} columns={columns} />
      <BottomBtn
        btns={[
          {
            title: '提交',
            onClick: form.handleSubmit(onSubmit)
          }
        ]}
      />
    </Fragment>
  )
}

export default withPage(Index)
