type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace upload {
    export class CtEbmBookingImageDTO {
      /** 预约ID */
      bookingId?: number;

      /** 主键 */
      ebmBookingImageId?: number;

      /** 自动生成的文件名 */
      fileName?: string;

      /** url中的相对路径 */
      relPath?: string;

      /** 临时uuid */
      uuid?: string;
    }

    export class CtEbmBusinessImageDTO {
      /** 业务办理id */
      businessId?: number;

      /** 主键 */
      ebmBusinessImageId?: number;

      /** 图片名 */
      fileName?: string;

      /** 图片的对于webcontent的相对路径 */
      relPath?: string;
    }

    export class HttpResult<T0 = any> {
      /** 返回状态code */
      code?: string;

      /** 错误信息 */
      errorMsg?: string;

      /** 具体的返回结果 */
      resultObj: T0;

      /** suc */
      suc?: boolean;
    }

    export class UploadMatFileParam {
      /** 用户账户id */
      accountId?: string;

      /** 由于上传目前还支持更新，因此如果带了 bookImageId ，就是更新 */
      bookImageId?: number;

      /** 同一个入职办理数据中文件的busUUID相同 */
      businessId?: string;

      /** 城市代码 */
      cityCode?: string;

      /** 文件名词 */
      fileName?: string;

      /** 访问腾讯下载图片的地址的素材ID */
      mediaId?: string;

      /** openId */
      openId?: string;

      /** 文件类型 */
      type?: string;
    }

    export class UploadResult {
      /** 图片的主键 */
      bookImageId?: number;

      /** 访问的url */
      url?: string;

      /** 图片唯一名 */
      uuid?: string;
    }
  }
}

declare namespace API {
  export namespace upload {
    /**
     * File Uploader Controller
     */
    export namespace fileUploader {
      /**
        * 删除预约业务办理附件
删除预约业务办理附件
        * /wx-upload/appointment/deleteAppointmentFile
        */
      export namespace deleteAppointmentFile {
        export class Params {
          /** 文件主键id */
          bookImageId: number;
        }

        export type Response = defs.upload.HttpResult<defs.upload.UploadResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查询预约业务办理附件
       * /wx-upload/appointment/fetchAppointmentFile
       */
      export namespace fetchAppointmentFile {
        export class Params {
          /** 当前用户的accountId */
          accountId: string;
          /** 预约办理的主键id */
          bookingId?: number;
          /** 文件主键id */
          ebmBookingImageId?: number;
          /** 作为临时主键的uuid */
          uuid: string;
        }

        export type Response = defs.upload.HttpResult<
          Array<defs.upload.CtEbmBookingImageDTO>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查询客服返回的办理附件
       * /wx-upload/appointment/fetchBusinessFile
       */
      export namespace fetchBusinessFile {
        export class Params {
          /** 业务办理ID */
          businessId: number;
          /** 文件主键id */
          ebmBusinessImageId?: number;
          /** empId */
          empId?: string;
        }

        export type Response = defs.upload.HttpResult<
          Array<defs.upload.CtEbmBusinessImageDTO>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 获取uuid
获取uuid
        * /wx-upload/appointment/fetchUUID
        */
      export namespace fetchUUID {
        export class Params {}

        export type Response = defs.upload.HttpResult<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
        * 上传预约业务办理附件
上传预约业务办理附件
        * /wx-upload/appointment/uploadAppointmentFile
        */
      export namespace uploadAppointmentFile {
        export class Params {}

        export type Response = defs.upload.HttpResult<defs.upload.UploadResult>;
        export const request: (
          data?: defs.upload.UploadMatFileParam,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.upload.UploadMatFileParam,
          options?: Taro.request.CommonUseRequestOption<defs.upload.UploadMatFileParam>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.upload.UploadMatFileParam
        >;
      }

      /**
        * form形式上传预约业务办理附件
form形式上传预约业务办理附件
        * /wx-upload/appointment/uploadAppointmentFileByForm
        */
      export namespace uploadAppointmentFileByForm {
        export class Params {}

        export type Response = defs.upload.HttpResult<defs.upload.UploadResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }

    /**
     * File View Controller
     */
    export namespace fileView {
      /**
       * 查看预约业务办理附件minio
       * /wx-upload/view/min/{type}/{accountId}/{fileName}
       */
      export namespace toViewMinio {
        export class Params {
          /** accountId */
          accountId: string;
          /** fileName */
          fileName: string;
          /** type */
          type: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查看预约业务办理附件NFS
       * /wx-upload/view/{type}/{accountId}/{fileName}
       */
      export namespace toView {
        export class Params {
          /** accountId */
          accountId: string;
          /** fileName */
          fileName: string;
          /** type */
          type: string;
        }

        export type Response = any;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }
    }
  }
}
