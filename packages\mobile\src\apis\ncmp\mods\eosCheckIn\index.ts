/**
 * @description 考勤打卡接口
 */
import * as getCheckFieldList from './getCheckFieldList';
import * as getCheckLogList from './getCheckLogList';
import * as getIsHasCheck from './getIsHasCheck';
import * as getIsInScope from './getIsInScope';
import * as getSysTime from './getSysTime';
import * as saveCheck from './saveCheck';
import * as saveCheck<PERSON>ield from './saveCheckField';
import * as saveCheckRepair from './saveCheckRepair';
import * as uploadFile from './uploadFile';

export {
  getCheckFieldList,
  getCheckLogList,
  getIsHasCheck,
  getIsInScope,
  getSysTime,
  saveCheck,
  saveCheckField,
  saveCheckRepair,
  uploadFile,
};
