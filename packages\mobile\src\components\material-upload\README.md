# MaterialUpload 材料上传组件

一个功能完整的材料上传下载组件，严格按照微信公众号SDK API实现，支持图片上传、下载、预览和删除功能。

## 功能特性

### 🚀 核心功能
- ✅ **微信SDK集成**: 严格按照微信公众号SDK API实现
- ✅ **文件上传**: 支持选择相册或拍照上传图片
- ✅ **文件下载**: 支持下载已上传的文件
- ✅ **图片预览**: 支持图片预览和缩放
- ✅ **文件管理**: 支持删除已上传的文件
- ✅ **状态管理**: 完整的上传状态跟踪（上传中、成功、失败）

### 🎨 用户体验
- ✅ **美观界面**: 参考上传图样式，界面美化
- ✅ **响应式设计**: 适配不同屏幕尺寸
- ✅ **交互反馈**: 丰富的动画效果和状态提示
- ✅ **错误处理**: 完善的错误处理和用户提示

### 🛡️ 技术特性
- ✅ **TypeScript**: 完整的类型定义和类型安全
- ✅ **性能优化**: 使用React Hooks优化渲染性能
- ✅ **错误边界**: 完善的错误处理机制
- ✅ **可扩展性**: 支持自定义配置和样式

## 使用方法

### 基础用法

```tsx
import { MaterialUpload } from '@components'

const MyComponent = () => {
  const material = {
    materialsId: '001',
    materialsName: '身份证',
    isOriginal: '1',
    materialsAccount: 1,
    isReturn: '1',
    isRequired: true,
    files: []
  }

  const handleFileChange = (files) => {
    console.log('文件变化:', files)
  }

  return (
    <MaterialUpload
      material={material}
      businessId="business-uuid"
      onFileChange={handleFileChange}
    />
  )
}
```

### 只读模式

```tsx
<MaterialUpload
  material={material}
  businessId="business-uuid"
  readonly={true}
/>
```

### 自定义样式

```tsx
<MaterialUpload
  material={material}
  businessId="business-uuid"
  className="custom-material-upload"
/>
```

## API 参考

### MaterialUploadProps

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| material | MaterialData | - | 材料数据（必填） |
| businessId | string | - | 业务UUID（必填） |
| readonly | boolean | false | 是否只读模式 |
| onFileChange | (files: UploadedFile[]) => void | - | 文件变化回调 |
| className | string | '' | 自定义样式类名 |

### MaterialData

| 属性 | 类型 | 说明 |
|------|------|------|
| materialsId | string | 材料ID |
| materialsName | string | 材料名称 |
| isOriginal | string | 是否原件（'1': 原件, '0': 复印件） |
| materialsAccount | number | 材料数量 |
| isReturn | string | 是否返还（'1': 返还, '0': 不返还） |
| isRequired | boolean | 是否必填 |
| files | UploadedFile[] | 已上传文件列表 |

### UploadedFile

| 属性 | 类型 | 说明 |
|------|------|------|
| id | string | 文件ID |
| name | string | 文件名称 |
| url | string | 文件URL |
| size | number | 文件大小（字节） |
| type | string | 文件类型 |
| status | FileStatus | 文件状态 |
| progress | number | 上传进度 |
| uploadTime | string | 上传时间 |

### FileStatus 枚举

```tsx
enum FileStatus {
  UPLOADING = 'uploading',  // 上传中
  SUCCESS = 'success',      // 上传成功
  ERROR = 'error'           // 上传失败
}
```

## 样式定制

### CSS 变量

组件支持通过CSS变量进行主题定制：

```scss
.custom-material-upload {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --error-color: #ff4d4f;
  --border-radius: 8px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}
```

### 样式类名

| 类名 | 说明 |
|------|------|
| .container | 组件容器 |
| .header | 头部信息区域 |
| .materialName | 材料名称 |
| .materialDetails | 材料详情 |
| .fileList | 文件列表 |
| .fileItem | 文件项 |
| .uploadSection | 上传区域 |
| .uploadBtn | 上传按钮 |

## 微信SDK配置

组件依赖微信SDK，需要确保以下配置：

### 1. 后端接口

确保后端提供微信签名接口：

```typescript
// 获取微信签名
pact.wxConfig.getSignature.request({ url: window.location.href })
```

### 2. 微信公众号配置

在微信公众号后台配置JS接口安全域名。

### 3. 权限配置

确保微信公众号具有以下权限：
- 选择图片权限
- 上传图片权限
- 下载图片权限
- 预览图片权限

## 错误处理

### 常见错误及解决方案

1. **微信配置失败**
   ```
   错误: 微信JSSDK配置失败
   解决: 检查微信签名接口和JS接口安全域名配置
   ```

2. **上传失败**
   ```
   错误: 上传图片失败
   解决: 检查网络连接和后端上传接口
   ```

3. **权限不足**
   ```
   错误: 选择图片失败
   解决: 检查微信公众号权限配置
   ```

## 最佳实践

### 1. 性能优化

```tsx
// 使用 useMemo 缓存计算结果
const fileIcon = useMemo(() => getFileIcon(fileName), [fileName])

// 使用 useCallback 缓存函数
const handleFileChange = useCallback((files) => {
  // 处理文件变化
}, [])
```

### 2. 错误处理

```tsx
try {
  await uploadFile()
} catch (error) {
  console.error('上传失败:', error)
  Taro.showToast({ title: '上传失败', icon: 'none' })
}
```

### 3. 用户体验

```tsx
// 显示上传进度
<Text className={styles.uploading}>上传中...</Text>

// 提供重试机制
<Button onClick={retryUpload}>重试</Button>
```

## 更新日志

### v1.0.0
- ✅ 初始版本发布
- ✅ 支持微信SDK图片上传下载
- ✅ 完整的TypeScript类型定义
- ✅ 美观的UI设计和交互效果

## 技术支持

如有问题或建议，请联系开发团队。
