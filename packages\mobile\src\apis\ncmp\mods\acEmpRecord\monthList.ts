/**
 * @description 查询月考勤记录
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** custId */
  custId: number;
  /** 年月（202404） */
  stMonth: string;
}

export type Result = defs.ncmp.HttpResult<ObjectMap>;
export const path = '/wx-ncmp/eos/ac/empRecord/stmonth/logList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
