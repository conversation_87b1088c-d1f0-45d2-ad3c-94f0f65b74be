# Buttons 组件

一个功能丰富、类型安全的按钮组件，支持多种图标、尺寸和状态。

## 特性

- ✅ **类型安全**: 完整的 TypeScript 支持
- ✅ **多种图标**: 支持详情、取消、订单等图标
- ✅ **多种尺寸**: 小、中、大三种尺寸
- ✅ **状态管理**: 支持禁用和加载状态
- ✅ **可定制**: 支持自定义样式和类名
- ✅ **无障碍**: 支持测试ID和键盘导航
- ✅ **响应式**: 支持hover和active状态

## 基本用法

```tsx
import Buttons, { ButtonIconType, ButtonSize } from '@components/buttons'

// 基本按钮
<Buttons 
  title="查看详情" 
  icon={ButtonIconType.DETAIL}
  onClick={() => console.log('clicked')}
/>

// 取消按钮
<Buttons 
  title="取消操作" 
  icon={ButtonIconType.CANCEL}
  onClick={handleCancel}
/>

// 订单按钮
<Buttons 
  title="提交订单" 
  icon={ButtonIconType.ORDER}
  onClick={handleSubmit}
/>
```

## 尺寸变体

```tsx
// 小尺寸
<Buttons 
  title="小按钮" 
  size={ButtonSize.SMALL}
  icon={ButtonIconType.DETAIL}
/>

// 中等尺寸（默认）
<Buttons 
  title="中等按钮" 
  size={ButtonSize.MEDIUM}
  icon={ButtonIconType.DETAIL}
/>

// 大尺寸
<Buttons 
  title="大按钮" 
  size={ButtonSize.LARGE}
  icon={ButtonIconType.DETAIL}
/>
```

## 状态管理

```tsx
// 禁用状态
<Buttons 
  title="禁用按钮" 
  disabled={true}
  icon={ButtonIconType.DETAIL}
/>

// 加载状态
<Buttons 
  title="提交中" 
  loading={true}
  icon={ButtonIconType.ORDER}
/>
```

## 自定义样式

```tsx
// 自定义类名
<Buttons 
  title="自定义按钮" 
  className="my-custom-button"
  icon={ButtonIconType.DETAIL}
/>
```

## API

### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| title | `string` | `''` | 按钮文本 |
| icon | `ButtonIconType \| string` | `ButtonIconType.DETAIL` | 按钮图标类型 |
| type | `ButtonType \| string` | - | 按钮类型（预留） |
| size | `ButtonSize \| number` | `ButtonSize.MEDIUM` | 按钮尺寸 |
| disabled | `boolean` | `false` | 是否禁用 |
| loading | `boolean` | `false` | 是否显示加载状态 |
| onClick | `() => void` | - | 点击事件处理函数 |
| className | `string` | `''` | 自定义样式类名 |
| testId | `string` | - | 测试ID |

### 枚举

#### ButtonIconType
- `DETAIL`: 详情图标
- `CANCEL`: 取消图标  
- `ORDER`: 订单图标

#### ButtonSize
- `SMALL`: 小尺寸
- `MEDIUM`: 中等尺寸
- `LARGE`: 大尺寸

#### ButtonType
- `PRIMARY`: 主要按钮
- `SECONDARY`: 次要按钮
- `DANGER`: 危险按钮

## 样式定制

组件使用 SCSS 模块化样式，支持以下 CSS 变量定制：

```scss
.custom-button {
  --button-bg-color: #your-color;
  --button-text-color: #your-text-color;
  --button-border-radius: 12px;
}
```

## 注意事项

1. 图标资源需要确保路径正确
2. 加载状态会自动禁用点击事件
3. 禁用状态会覆盖所有交互效果
4. 建议使用枚举值而不是字符串以获得更好的类型安全

## 更新日志

### v2.0.0
- ✅ 重构为 TypeScript
- ✅ 添加类型安全支持
- ✅ 优化样式结构
- ✅ 添加状态管理
- ✅ 改进无障碍支持
- ✅ 添加尺寸变体
