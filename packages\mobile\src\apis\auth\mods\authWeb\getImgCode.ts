/**
 * @description 获取图形验证码
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** 平台 1:公众号 2:企微 3:钉钉 4:飞书 */
  platform: number;
  /** 用户唯一ID，getTokenByCode返回 */
  platformOpenid: string;
}

export type Result = defs.auth.GlobalResult<defs.auth.VcodeResp>;
export const path = '/enterprise-auth/web/getImgCode';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
