type ObjectMap<Key extends string | number | symbol = any, Value = any> = {
  [key in Key]: Value;
};

declare namespace defs {
  export namespace hss {
    export class FeedBack {
      /** content */
      content?: string;

      /** title */
      title?: string;
    }

    export class GeneralRespBean<T0 = any> {
      /** code */
      code?: string;

      /** data */
      data: T0;

      /** msg */
      msg?: string;
    }

    export class HsAuth {
      /** createTime */
      createTime?: string;

      /** customerId */
      customerId?: string;

      /** customerName */
      customerName?: string;

      /** declareId */
      declareId?: string;

      /** hsType */
      hsType?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** idType */
      idType?: string;

      /** name */
      name?: string;

      /** openid */
      openid?: string;

      /** personId */
      personId?: string;

      /** phone */
      phone?: string;

      /** status1 */
      status1?: string;

      /** status2 */
      status2?: string;

      /** updateTime */
      updateTime?: string;

      /** vCode */
      vCode?: string;
    }

    export class HsBankInfo {
      /** bankAccount */
      bankAccount?: string;

      /** bankName */
      bankName?: string;

      /** basicId */
      basicId?: number;

      /** createTime */
      createTime?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** openBankCity */
      openBankCity?: string;

      /** openBankCityid */
      openBankCityid?: string;

      /** openBankName */
      openBankName?: string;

      /** openBankProvince */
      openBankProvince?: string;

      /** openBankProvinceid */
      openBankProvinceid?: string;

      /** openid */
      openid?: string;

      /** updateBy */
      updateBy?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class HsBasicInfo {
      /** address */
      address?: string;

      /** createTime */
      createTime?: string;

      /** email */
      email?: string;

      /** hsCity */
      hsCity?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** idType */
      idType?: string;

      /** liveChinaDays */
      liveChinaDays?: string;

      /** name */
      name?: string;

      /** openid */
      openid?: string;

      /** phoneNumber */
      phoneNumber?: string;

      /** status */
      status?: string;

      /** taxYearDays */
      taxYearDays?: string;

      /** taxpayerNumber */
      taxpayerNumber?: string;

      /** term */
      term?: string;

      /** type */
      type?: string;

      /** updateBy */
      updateBy?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class HsData {
      /** data */
      data?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** num */
      num?: number;

      /** openid */
      openid?: string;

      /** payStatus */
      payStatus?: string;

      /** payStatusStr */
      payStatusStr?: string;

      /** status */
      status?: string;

      /** statusStr */
      statusStr?: string;

      /** updateBy */
      updateBy?: string;

      /** updateTime */
      updateTime?: string;
    }

    export class HsDeductionOtherInfo {
      /** allowDeductDonation */
      allowDeductDonation?: string;

      /** allowDeductTax */
      allowDeductTax?: string;

      /** annuity */
      annuity?: string;

      /** basicId */
      basicId?: number;

      /** healthInsurance */
      healthInsurance?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** openid */
      openid?: string;

      /** other */
      other?: string;

      /** propertyOldValue */
      propertyOldValue?: string;

      /** remarks */
      remarks?: string;

      /** taxDelayInsurance */
      taxDelayInsurance?: string;

      /** taxPaid */
      taxPaid?: string;
    }

    export class HsDeductionSpecialInfo {
      /** attribute1 */
      attribute1?: string;

      /** attribute2 */
      attribute2?: string;

      /** attribute3 */
      attribute3?: string;

      /** attribute4 */
      attribute4?: string;

      /** basicId */
      basicId?: number;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** openid */
      openid?: string;

      /** segment1 */
      segment1?: string;

      /** segment2 */
      segment2?: string;

      /** segment3 */
      segment3?: string;

      /** segment4 */
      segment4?: string;

      /** segment5 */
      segment5?: string;

      /** segment6 */
      segment6?: string;
    }

    export class HsIncomeInfo {
      /** basicId */
      basicId?: number;

      /** bonus */
      bonus?: string;

      /** city */
      city?: string;

      /** draftWages */
      draftWages?: string;

      /** franchiseWages */
      franchiseWages?: string;

      /** hsYear */
      hsYear?: string;

      /** id */
      id?: number;

      /** idCard */
      idCard?: string;

      /** labourWages */
      labourWages?: string;

      /** openid */
      openid?: string;

      /** salary */
      salary?: string;
    }

    export class HsResult {
      /** segment1 */
      segment1?: string;

      /** segment10 */
      segment10?: string;

      /** segment11 */
      segment11?: string;

      /** segment12 */
      segment12?: string;

      /** segment13 */
      segment13?: string;

      /** segment2 */
      segment2?: string;

      /** segment3 */
      segment3?: string;

      /** segment4 */
      segment4?: string;

      /** segment5 */
      segment5?: string;

      /** segment6 */
      segment6?: string;

      /** segment7 */
      segment7?: string;

      /** segment8 */
      segment8?: string;

      /** segment9 */
      segment9?: string;

      /** status */
      status?: string;

      /** statusStr */
      statusStr?: string;

      /** year */
      year?: string;
    }

    export class Map<T0 = any, T1 = any> {}

    export class TemplateMsg {
      /** errorList */
      errorList?: defs.hss.FeedBack;

      /** first */
      first?: string;

      /** idCard */
      idCard?: string;

      /** keyword1 */
      keyword1?: string;

      /** keyword2 */
      keyword2?: string;

      /** keyword3 */
      keyword3?: string;

      /** keyword4 */
      keyword4?: string;

      /** openid */
      openid?: string;

      /** remark */
      remark?: string;

      /** typeCode */
      typeCode?: string;
    }
  }
}

declare namespace API {
  export namespace hss {
    /**
     * Controller
     */
    export namespace controller {
      /**
       * 身份验证提交
       * /yc-hs/api/authentication
       */
      export namespace authentication {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
        export const request: (
          data?: defs.hss.HsAuth,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsAuth,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsAuth>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.HsAuth>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace paySuccess {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace headCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace postCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace putCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace deleteCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace optionsCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * paySuccess
       * /yc-hs/api/callback
       */
      export namespace patchCallback {
        export class Params {}

        export type Response = string;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据身份证号获取汇算认证信息
       * /yc-hs/api/getAuthByIdCard/{idCard}
       */
      export namespace getAuthByIdCard {
        export class Params {
          /** idCard */
          idCard: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据openID查询汇算认证信息
       * /yc-hs/api/getAuthByOpenid/{openid}
       */
      export namespace getAuthByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据openid获取汇算基本信息
       * /yc-hs/api/getBasicInfoByOpenid/{openid}
       */
      export namespace getBasicInfoByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsBasicInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 验证数据完成度，判断跳转页面
       * /yc-hs/api/getHsDataStatus/{openid}
       */
      export namespace getHsDataStatus {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取人员汇算类型及客户
       * /yc-hs/api/getHsType
       */
      export namespace getHsType {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsAuth>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
       * 4 位图片验证码
       * /yc-hs/api/getVcode/{openid}
       */
      export namespace getVcode {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 新增汇算基本信息
       * /yc-hs/api/insertBasicInfo
       */
      export namespace insertBasicInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsBasicInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsBasicInfo,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsBasicInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.HsBasicInfo>;
      }

      /**
       * 判断当前时间是否还可以进行汇算
       * /yc-hs/api/isExpire
       */
      export namespace isExpire {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 下订单,返回页面参数
       * /yc-hs/api/orders/{openid}
       */
      export namespace orders {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<ObjectMap>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据openID查询身份证号
       * /yc-hs/api/selectIdCardByOpenid/{openid}
       */
      export namespace selectIdCardByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取短信验证码
       * /yc-hs/api/sendSMS
       */
      export namespace sendSMS {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
       * 大户/单立户委托发送短信验证码
       * /yc-hs/api/sendSMSBig
       */
      export namespace sendSMSBig {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
       * 获取短信验证码 - 个人
       * /yc-hs/api/sendSMSPersonal
       */
      export namespace sendSMSPersonal {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
       * sendTemplateMsg
       * /yc-hs/api/template-msg/send/personalInformationApplicationFailed
       */
      export namespace sendTemplateMsg {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.TemplateMsg,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.TemplateMsg,
          options?: Taro.request.CommonUseRequestOption<defs.hss.TemplateMsg>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.TemplateMsg>;
      }

      /**
       * 签协议
       * /yc-hs/api/updateAuthByIDCard
       */
      export namespace updateAuthByIDCard {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsAuth,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsAuth,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsAuth>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.HsAuth>;
      }
    }

    /**
     * Hs Controller
     */
    export namespace hs {
      /**
       * 申报确认
       * /yc-hs/hsInfo/HsDeclareConfirm/{openid}
       */
      export namespace HsDeclareConfirm {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取银行卡信息
       * /yc-hs/hsInfo/getBankInfoByOpenid/{openid}
       */
      export namespace getBankInfoByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsBankInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查询汇算列表
       * /yc-hs/hsInfo/getDataListByOpenid
       */
      export namespace getDataListByOpenid {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<Array<defs.hss.HsData>>;
        export const request: (
          data?: object,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: object,
          options?: Taro.request.CommonUseRequestOption<object>,
        ) => Taro.request.CommonUseResultType<Response, object>;
      }

      /**
       * 获取其他加扣信息
       * /yc-hs/hsInfo/getDeductionOtherInfoByOpenid/{openid}
       */
      export namespace getDeductionOtherInfoByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response =
          defs.hss.GeneralRespBean<defs.hss.HsDeductionOtherInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取专项扣除信息
       * /yc-hs/hsInfo/getDeductionSpecialInfoByOpenid/{openid}
       */
      export namespace getDeductionSpecialInfoByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response =
          defs.hss.GeneralRespBean<defs.hss.HsDeductionSpecialInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 查询汇算城市
       * /yc-hs/hsInfo/getHsCity/{openid}/{cityName}
       */
      export namespace getHsCity {
        export class Params {
          /** cityName */
          cityName: string;
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<
          Array<ObjectMap<string, string>>
        >;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 根据年份查询汇算信息详情
       * /yc-hs/hsInfo/getHsDataByYear/{openid}/{year}
       */
      export namespace getHsDataByYear {
        export class Params {
          /** openid */
          openid: string;
          /** year */
          year: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 申报查询
       * /yc-hs/hsInfo/getHsDeclare/{openid}
       */
      export namespace getHsDeclare {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 申报结果查询
       * /yc-hs/hsInfo/getHsResult/{openid}/{year}
       */
      export namespace getHsResult {
        export class Params {
          /** openid */
          openid: string;
          /** year */
          year: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsResult>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 获取收入信息
       * /yc-hs/hsInfo/getIncomeInfoByOpenid/{openid}
       */
      export namespace getIncomeInfoByOpenid {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<defs.hss.HsIncomeInfo>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 新增银行卡信息
       * /yc-hs/hsInfo/insertBankInfo
       */
      export namespace insertBankInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsBankInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsBankInfo,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsBankInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.HsBankInfo>;
      }

      /**
       * 新增其他扣除信息
       * /yc-hs/hsInfo/insertDeductionOtherInfo
       */
      export namespace insertDeductionOtherInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsDeductionOtherInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsDeductionOtherInfo,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsDeductionOtherInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.hss.HsDeductionOtherInfo
        >;
      }

      /**
       * 新增专项扣除信息
       * /yc-hs/hsInfo/insertDeductionSpecialInfo
       */
      export namespace insertDeductionSpecialInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsDeductionSpecialInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsDeductionSpecialInfo,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsDeductionSpecialInfo>,
        ) => Taro.request.CommonUseResultType<
          Response,
          defs.hss.HsDeductionSpecialInfo
        >;
      }

      /**
       * 新增收入信息
       * /yc-hs/hsInfo/insertIncomeInfo
       */
      export namespace insertIncomeInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: defs.hss.HsIncomeInfo,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: defs.hss.HsIncomeInfo,
          options?: Taro.request.CommonUseRequestOption<defs.hss.HsIncomeInfo>,
        ) => Taro.request.CommonUseResultType<Response, defs.hss.HsIncomeInfo>;
      }

      /**
       * 提交汇算信息
       * /yc-hs/hsInfo/submitHs/{openid}
       */
      export namespace submitHs {
        export class Params {
          /** openid */
          openid: string;
        }

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: Params,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: Params,
          options?: Taro.request.CommonUseRequestOption<Params>,
        ) => Taro.request.CommonUseResultType<Response, Params>;
      }

      /**
       * 更新支付信息
       * /yc-hs/hsInfo/updatePayInfo
       */
      export namespace updatePayInfo {
        export class Params {}

        export type Response = defs.hss.GeneralRespBean<string>;
        export const request: (
          data?: string,
          options?: Taro.request.CommonOption,
        ) => Promise<Response>;
        export const useRequest: (
          data?: string,
          options?: Taro.request.CommonUseRequestOption<string>,
        ) => Taro.request.CommonUseResultType<Response, string>;
      }
    }
  }
}
