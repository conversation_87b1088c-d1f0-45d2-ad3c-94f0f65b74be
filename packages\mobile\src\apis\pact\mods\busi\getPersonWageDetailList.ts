/**
 * @description 工资详情列表接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** classId */
  classId?: string;
  /** empId */
  empId?: string;
  /** openId */
  openId?: string;
  /** paytype */
  paytype?: string;
  /** sendId */
  sendId?: string;
  /** sendMonth */
  sendMonth?: string;
}

export type Result = defs.pact.WageDetailListRespBean;
export const path = '/yc-wepact-mobile/busi/getPersonWageDetailList';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
