import Taro from '@tarojs/taro'
import { isWx } from '@utils'
import { pact } from '@apis/pact'

// 微信JSSDK配置接口
export interface WxConfig {
  appId: string
  timestamp: number
  nonceStr: string
  signature: string
  jsApiList: string[]
}

// 微信JSSDK配置管理类
class WxConfigManager {
  private isConfigured = false
  private configPromise: Promise<void> | null = null

  /**
   * 配置微信JSSDK
   * @param jsApiList 需要使用的JS接口列表
   * @returns Promise<void>
   */
  async configWx(jsApiList: string[] = ['chooseImage', 'uploadImage', 'downloadImage', 'previewImage']): Promise<void> {
    if (!isWx()) {
      throw new Error('当前环境不是微信浏览器')
    }

    // 如果已经在配置中，返回配置Promise
    if (this.configPromise) {
      return this.configPromise
    }

    // 如果已经配置过，直接返回
    if (this.isConfigured) {
      return Promise.resolve()
    }

    this.configPromise = this.doConfig(jsApiList)
    return this.configPromise
  }

  /**
   * 执行微信JSSDK配置
   * @param jsApiList JS接口列表
   */
  private async doConfig(jsApiList: string[]): Promise<void> {
    try {
      // 获取当前页面URL（去除hash部分）
      const url = window.location.href.split('#')[0]
      
      // 调用后端接口获取签名信息
      const signatureResult = await pact.wxConfig.getSignature.request({ url })
      
      if (!signatureResult || !signatureResult.appid) {
        throw new Error('获取微信签名失败')
      }

      // 配置微信JSSDK
      const config: WxConfig = {
        appId: signatureResult.appid,
        timestamp: signatureResult.timestamp,
        nonceStr: signatureResult.noncestr,
        signature: signatureResult.signature,
        jsApiList
      }

      return new Promise<void>((resolve, reject) => {
        wx.config({
          debug: false, // 生产环境设为false
          ...config
        })

        wx.ready(() => {
          console.log('微信JSSDK配置成功')
          this.isConfigured = true
          this.configPromise = null
          resolve()
        })

        wx.error((error) => {
          console.error('微信JSSDK配置失败:', error)
          this.isConfigured = false
          this.configPromise = null
          reject(new Error(`微信JSSDK配置失败: ${JSON.stringify(error)}`))
        })
      })
    } catch (error) {
      this.configPromise = null
      throw error
    }
  }

  /**
   * 选择图片
   * @param options 选择图片选项
   */
  async chooseImage(options: {
    count?: number
    sizeType?: string[]
    sourceType?: string[]
  } = {}): Promise<string[]> {
    await this.configWx(['chooseImage'])

    const {
      count = 9,
      sizeType = ['original', 'compressed'],
      sourceType = ['album', 'camera']
    } = options

    return new Promise((resolve, reject) => {
      wx.chooseImage({
        count,
        sizeType,
        sourceType,
        success: (res) => {
          resolve(res.localIds)
        },
        fail: (error) => {
          reject(new Error(`选择图片失败: ${JSON.stringify(error)}`))
        }
      })
    })
  }

  /**
   * 上传图片到微信服务器
   * @param localId 本地图片ID
   * @param isShowProgressTips 是否显示进度提示
   */
  async uploadImage(localId: string, isShowProgressTips = 1): Promise<string> {
    await this.configWx(['uploadImage'])

    return new Promise((resolve, reject) => {
      wx.uploadImage({
        localId,
        isShowProgressTips,
        success: (res) => {
          resolve(res.serverId)
        },
        fail: (error) => {
          reject(new Error(`上传图片失败: ${JSON.stringify(error)}`))
        }
      })
    })
  }

  /**
   * 下载图片
   * @param serverId 服务器图片ID
   * @param isShowProgressTips 是否显示进度提示
   */
  async downloadImage(serverId: string, isShowProgressTips = 1): Promise<string> {
    await this.configWx(['downloadImage'])

    return new Promise((resolve, reject) => {
      wx.downloadImage({
        serverId,
        isShowProgressTips,
        success: (res) => {
          resolve(res.localId)
        },
        fail: (error) => {
          reject(new Error(`下载图片失败: ${JSON.stringify(error)}`))
        }
      })
    })
  }

  /**
   * 预览图片
   * @param current 当前显示图片的链接
   * @param urls 需要预览的图片链接列表
   */
  async previewImage(current: string, urls: string[]): Promise<void> {
    await this.configWx(['previewImage'])

    return new Promise((resolve, reject) => {
      wx.previewImage({
        current,
        urls,
        success: () => {
          resolve()
        },
        fail: (error) => {
          reject(new Error(`预览图片失败: ${JSON.stringify(error)}`))
        }
      })
    })
  }

  /**
   * 获取网络状态
   */
  async getNetworkType(): Promise<string> {
    await this.configWx(['getNetworkType'])

    return new Promise((resolve, reject) => {
      wx.getNetworkType({
        success: (res) => {
          resolve(res.networkType)
        },
        fail: (error) => {
          reject(new Error(`获取网络状态失败: ${JSON.stringify(error)}`))
        }
      })
    })
  }

  /**
   * 重置配置状态（用于调试）
   */
  reset(): void {
    this.isConfigured = false
    this.configPromise = null
  }

  /**
   * 检查是否已配置
   */
  get configured(): boolean {
    return this.isConfigured
  }
}

// 导出单例实例
export const wxConfigManager = new WxConfigManager()

// 便捷方法导出
export const {
  configWx,
  chooseImage: wxChooseImage,
  uploadImage: wxUploadImage,
  downloadImage: wxDownloadImage,
  previewImage: wxPreviewImage,
  getNetworkType: wxGetNetworkType
} = wxConfigManager

// 默认导出
export default wxConfigManager
