/**
 * @description Policy Controller
 */
import * as downloadPolicyFile from './downloadPolicyFile';
import * as getBusType from './getBusType';
import * as getBusnameClass from './getBusnameClass';
import * as getEmpCity from './getEmpCity';
import * as getMaterial from './getMaterial';
import * as getSingleFringeBenefits from './getSingleFringeBenefits';
import * as getSubType from './getSubType';
import * as getSubTypeByIds from './getSubTypeByIds';
import * as sendMaterialMail from './sendMaterialMail';

export {
  downloadPolicyFile,
  getBusType,
  getBusnameClass,
  getEmpCity,
  getMaterial,
  getSingleFringeBenefits,
  getSubType,
  getSubTypeByIds,
  sendMaterialMail,
};
