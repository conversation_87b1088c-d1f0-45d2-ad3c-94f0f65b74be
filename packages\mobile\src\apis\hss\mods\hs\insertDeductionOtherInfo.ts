/**
 * @description 新增其他扣除信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/hsInfo/insertDeductionOtherInfo';
export const method = 'POST';
export const request = (
  data: defs.hss.HsDeductionOtherInfo,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.hss.HsDeductionOtherInfo,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
