/**
 * @description 获取社保实作进度
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.EleContract>;
export const path = '/wx-ncmp/ss/getSsProcessList';
export const method = 'POST';
export const request = (
  data: defs.ncmp.SsProcessDto,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.SsProcessDto,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
