class AccountInfo {
  /** accountId */
  accountId = '';

  /** idCardNum */
  idCardNum = '';

  /** mobilePhoneNum */
  mobilePhoneNum = '';

  /** openId */
  openId = '';

  /** verificationCode */
  verificationCode = '';
}

class AppBindInfo {
  /** accountId */
  accountId = '';

  /** bindStatus */
  bindStatus = false;

  /** bindTime */
  bindTime = '';

  /** credentialsType */
  credentialsType = '';

  /** empId */
  empId = '';

  /** globalToken */
  globalToken = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** latestToken */
  latestToken = '';

  /** mobile */
  mobile = '';

  /** openId */
  openId = '';

  /** token */
  token = '';

  /** updateTime */
  updateTime = '';
}

class AppointmentCancelBean {
  /** code */
  code = '';

  /** message */
  message = '';
}

class AppointmentCancelParam {
  /** bookingId */
  bookingId = '';

  /** cancelReason */
  cancelReason = '';

  /** empId */
  empId = '';

  /** openId */
  openId = '';
}

class AppointmentDetailBean {
  /** code */
  code = '';

  /** data */
  data = new AppointmentDetailData();

  /** message */
  message = '';
}

class AppointmentDetailData {
  /** bookingDt */
  bookingDt = '';

  /** bookingRemark */
  bookingRemark = '';

  /** bookingStatus */
  bookingStatus = '';

  /** busSubTypeName */
  busSubTypeName = '';

  /** busTypeName */
  busTypeName = '';

  /** cancelReason */
  cancelReason = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** contactTel */
  contactTel = '';

  /** createDt */
  createDt = '';

  /** departmentName */
  departmentName = '';

  /** empName */
  empName = '';

  /** idCardNum */
  idCardNum = '';

  /** mobilePhoneNum */
  mobilePhoneNum = '';

  /** updateDt */
  updateDt = '';
}

class AppointmentListBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class AppointmentListData {
  /** bookingDt */
  bookingDt = '';

  /** bookingId */
  bookingId = '';

  /** bookingStatus */
  bookingStatus = '';

  /** busSubTypeName */
  busSubTypeName = '';

  /** busTypeName */
  busTypeName = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** createDt */
  createDt = '';
}

class AppointmentNumBean {
  /** code */
  code = '';

  /** data */
  data = new AppointmentNumData();

  /** message */
  message = '';
}

class AppointmentNumData {
  /** code */
  code = undefined;

  /** data */
  data = undefined;
}

class AuthParamBean {
  /** accountInfo */
  accountInfo = new AccountInfo();

  /** openId */
  openId = '';
}

class AuthResponseBean {
  /** code */
  code = '';

  /** data */
  data = new AuthResponseData();

  /** message */
  message = '';
}

class AuthResponseData {
  /** cmpToken */
  cmpToken = '';

  /** empId */
  empId = '';

  /** result */
  result = '';
}

class BusinessDetailBean {
  /** code */
  code = '';

  /** data */
  data = new BusinessDetailData();

  /** message */
  message = '';
}

class BusinessDetailData {
  /** busSubTypeName */
  busSubTypeName = '';

  /** busTypeName */
  busTypeName = '';

  /** businessId */
  businessId = '';

  /** businessStatus */
  businessStatus = '';

  /** pAgent */
  pAgent = '';

  /** pOperator */
  pOperator = '';

  /** procResult */
  procResult = '';

  /** procStartDt */
  procStartDt = '';

  /** procStatus */
  procStatus = '';

  /** processorType */
  processorType = '';

  /** result */
  result = '';
}

class BusinessItemBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class BusinessItemData {
  /** key */
  key = '';

  /** value */
  value = '';
}

class BusinessListBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class BusinessListData {
  /** busSubTypeName */
  busSubTypeName = '';

  /** busTypeName */
  busTypeName = '';

  /** businessId */
  businessId = '';

  /** businessStatus */
  businessStatus = '';

  /** categoryId */
  categoryId = '';

  /** categoryName */
  categoryName = '';

  /** createDt */
  createDt = '';
}

class ButlerResponseBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';

  /** result */
  result = undefined;
}

class ButlerResponseData {
  /** address */
  address = '';

  /** contact */
  contact = '';

  /** email */
  email = '';

  /** providerType */
  providerType = '';

  /** stewardName */
  stewardName = '';
}

class CityBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class CityData {
  /** key */
  key = '';

  /** value */
  value = '';
}

class ClcData {
  /** 表单配置 */
  clcTemplateInfo = new ClcTemplateInfo();

  /** 表单数据 */
  jsonData = '';

  /** 图片数据 */
  photos = [];
}

class ClcTemplateInfo {
  /** createBy */
  createBy = '';

  /** createTime */
  createTime = '';

  /** creatorName */
  creatorName = '';

  /** id */
  id = '';

  /** isDelete */
  isDelete = undefined;

  /** status */
  status = '';

  /** templateCode */
  templateCode = '';

  /** templateId */
  templateId = '';

  /** templateJson */
  templateJson = '';

  /** updateBy */
  updateBy = '';

  /** updateName */
  updateName = '';

  /** updateTime */
  updateTime = '';

  /** version */
  version = '';

  /** versionName */
  versionName = '';
}

class CompanyResponseBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class CompanyResponseData {
  /** address */
  address = '';

  /** areaId */
  areaId = undefined;

  /** cityId */
  cityId = undefined;

  /** contactTel */
  contactTel = '';

  /** id */
  id = undefined;

  /** key */
  key = '';

  /** name */
  name = '';

  /** value */
  value = '';
}

class DynamicFormReqBean {
  /** birthdate */
  birthdate = '';

  /** cityCode */
  cityCode = '';

  /** idCard */
  idCard = '';

  /** name */
  name = '';

  /** openId */
  openId = '';

  /** pageNo */
  pageNo = undefined;

  /** sex */
  sex = '';

  /** uuid */
  uuid = '';
}

class EchoFileSubmitReqBean {
  /** accountId */
  accountId = '';

  /** cityCode */
  cityCode = '';

  /** newBusId */
  newBusId = '';

  /** oldBusId */
  oldBusId = '';

  /** openId */
  openId = '';
}

class EchoFileSubmitResp {
  /** address */
  address = '';

  /** code */
  code = '';

  /** data */
  data = '';

  /** idCard */
  idCard = '';

  /** message */
  message = '';

  /** name */
  name = '';

  /** sex */
  sex = '';

  /** validEnd */
  validEnd = '';

  /** validStart */
  validStart = '';
}

class EntryParam {
  /** businessId */
  businessId = '';

  /** cityCode */
  cityCode = '';

  /** formContent */
  formContent = '';

  /** openid */
  openid = '';

  /** token */
  token = '';
}

class EntryResponseBean {
  /** code */
  code = '';

  /** message */
  message = '';
}

class FeedbackBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class FeedbackData {
  /** businessId */
  businessId = undefined;

  /** categoryName */
  categoryName = '';

  /** cetfId */
  cetfId = undefined;

  /** fbContext */
  fbContext = '';

  /** fbDate */
  fbDate = '';

  /** isRead */
  isRead = undefined;
}

class FollowResponse {
  /** code */
  code = '';

  /** data */
  data = new FollowResponseData();

  /** message */
  message = '';
}

class FollowResponseData {
  /** accountId */
  accountId = '';

  /** cmpToken */
  cmpToken = '';
}

class FollowerInfo {
  /** accountId */
  accountId = '';

  /** appId */
  appId = '';

  /** city */
  city = '';

  /** country */
  country = '';

  /** createtime */
  createtime = '';

  /** delFlag */
  delFlag = false;

  /** groupId */
  groupId = undefined;

  /** headImgUrl */
  headImgUrl = '';

  /** id */
  id = undefined;

  /** language */
  language = '';

  /** lastSubscribeTime */
  lastSubscribeTime = '';

  /** nickname */
  nickname = '';

  /** openId */
  openId = '';

  /** province */
  province = '';

  /** queryNickname */
  queryNickname = '';

  /** remark */
  remark = '';

  /** remarkNickname */
  remarkNickname = '';

  /** sex */
  sex = '';

  /** subscribe */
  subscribe = '';

  /** subscribeTime */
  subscribeTime = '';

  /** tagidList */
  tagidList = '';

  /** thumbnail */
  thumbnail = '';

  /** unionId */
  unionId = '';

  /** unsubscribeTime */
  unsubscribeTime = '';
}

class FundData {
  /** code */
  code = '';

  /** eTotalAmt */
  eTotalAmt = '';

  /** list */
  list = [];

  /** message */
  message = '';

  /** pTotalAmt */
  pTotalAmt = '';
}

class FundDetailBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class FundDetailData {
  /** amt */
  amt = '';

  /** city */
  city = '';

  /** eAdditionalAmt */
  eAdditionalAmt = '';

  /** eAmt */
  eAmt = '';

  /** eBase */
  eBase = '';

  /** eRatio */
  eRatio = '';

  /** pAdditionalAmt */
  pAdditionalAmt = '';

  /** pAmt */
  pAmt = '';

  /** pBase */
  pBase = '';

  /** pRatio */
  pRatio = '';

  /** productName */
  productName = '';

  /** serviceMonth */
  serviceMonth = '';
}

class FundDetailParam {
  /** category */
  category = '';

  /** empId */
  empId = '';

  /** openId */
  openId = '';

  /** serviceMonth */
  serviceMonth = '';
}

class FundLiatData {
  /** amt */
  amt = '';

  /** custName */
  custName = '';

  /** eAmt */
  eAmt = '';

  /** pAmt */
  pAmt = '';

  /** serviceMonth */
  serviceMonth = '';
}

class FundParam {
  /** openId */
  openId = '';

  /** paramMap */
  paramMap = new ParamMap();
}

class HosResponseBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class HosResponseData {
  /** hosCode */
  hosCode = '';

  /** hosCountyName */
  hosCountyName = '';

  /** hosLevel */
  hosLevel = '';

  /** hosType */
  hosType = '';

  /** key */
  key = '';

  /** value */
  value = '';
}

class IdCardComposePram {
  /** accountId */
  accountId = '';

  /** businessId */
  businessId = '';

  /** cityCode */
  cityCode = '';

  /** endMediaId */
  endMediaId = '';

  /** endUrl */
  endUrl = '';

  /** frontMediaId */
  frontMediaId = '';

  /** frontUrl */
  frontUrl = '';

  /** openId */
  openId = '';

  /** type */
  type = '';
}

class IdCardComposeRespBean {
  /** code */
  code = '';

  /** data */
  data = '';

  /** message */
  message = '';
}

class InsertParam {
  /** accountInfo */
  accountInfo = new AccountInfo();

  /** bookingDt */
  bookingDt = '';

  /** bookingRemark */
  bookingRemark = '';

  /** busSubTypeIdStr */
  busSubTypeIdStr = '';

  /** busTypeId */
  busTypeId = '';

  /** cancelReason */
  cancelReason = '';

  /** categoryId */
  categoryId = '';

  /** cityId */
  cityId = '';

  /** createBy */
  createBy = '';

  /** dateType */
  dateType = '';

  /** departmentId */
  departmentId = '';

  /** openId */
  openId = '';

  /** uuid */
  uuid = '';
}

class InsertResponseBean {
  /** code */
  code = '';

  /** message */
  message = '';
}

class JsonMessage {
  /** data */
  data = undefined;

  /** errorCode */
  errorCode = '';

  /** errorMessage */
  errorMessage = '';

  /** successful */
  successful = false;
}

class Map {}

class MaterBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class MaterData {
  /** busSubtypeId */
  busSubtypeId = undefined;

  /** isReturn */
  isReturn = undefined;

  /** materialsAccount */
  materialsAccount = undefined;

  /** materialsInfo */
  materialsInfo = new MaterialsInfo();
}

class MaterialsInfo {
  /** isOriginal */
  isOriginal = '';

  /** materialsId */
  materialsId = '';

  /** materialsName */
  materialsName = '';
}

class MenuResponseBean {
  /** code */
  code = undefined;

  /** data */
  data = new MenuResponseData();

  /** message */
  message = '';
}

class MenuResponseData {
  /** view_Implementation */
  view_Implementation = undefined;

  /** view_order */
  view_order = undefined;

  /** view_providentFund */
  view_providentFund = undefined;

  /** view_salary */
  view_salary = undefined;

  /** view_socialInsurance */
  view_socialInsurance = undefined;
}

class MoreCityInfo {
  /** code */
  code = '';

  /** name */
  name = '';

  /** orderValue */
  orderValue = undefined;

  /** provinceCode */
  provinceCode = '';
}

class OcrParamBean {
  /** empId */
  empId = undefined;

  /** mediaId */
  mediaId = '';

  /** openid */
  openid = '';
}

class OcrResponseBean {
  /** code */
  code = undefined;

  /** data */
  data = new OcrResponseData();

  /** message */
  message = '';
}

class OcrResponseData {
  /** id_code */
  id_code = '';

  /** name */
  name = '';

  /** openid */
  openid = '';
}

class ParamMap {
  /** category */
  category = undefined;

  /** empId */
  empId = undefined;

  /** endMonth */
  endMonth = '';

  /** pageNum */
  pageNum = undefined;

  /** pageSize */
  pageSize = undefined;

  /** startMonth */
  startMonth = '';
}

class PersonInfoRespBean {
  /** code */
  code = '';

  /** data */
  data = new PersonInfoRespData();

  /** message */
  message = '';
}

class PersonInfoRespData {
  /** accountId */
  accountId = '';

  /** bankAccount */
  bankAccount = '';

  /** bankCity */
  bankCity = '';

  /** bankName */
  bankName = '';

  /** birthday */
  birthday = '';

  /** company */
  company = '';

  /** currentSalary */
  currentSalary = '';

  /** educationLevel */
  educationLevel = '';

  /** email */
  email = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** ethnic */
  ethnic = '';

  /** gender */
  gender = '';

  /** healthStatus */
  healthStatus = '';

  /** idCardNegative */
  idCardNegative = '';

  /** idCardNum */
  idCardNum = '';

  /** idCardPositive */
  idCardPositive = '';

  /** jobStatus */
  jobStatus = '';

  /** mobilePhoneNum */
  mobilePhoneNum = '';

  /** otherBankName */
  otherBankName = '';

  /** picUrl */
  picUrl = '';

  /** politicalStatus */
  politicalStatus = '';

  /** relation */
  relation = [];

  /** residentAddress */
  residentAddress = '';

  /** residentCity */
  residentCity = '';

  /** residentProvince */
  residentProvince = '';

  /** residentZipCode */
  residentZipCode = '';

  /** socialSecurity */
  socialSecurity = [];

  /** subbranch */
  subbranch = '';

  /** workExperience */
  workExperience = '';
}

class PersonRelationData {
  /** birthDate */
  birthDate = '';

  /** idCardNum */
  idCardNum = '';

  /** name */
  name = '';

  /** proofRelPath */
  proofRelPath = '';

  /** relationship */
  relationship = '';
}

class PersonSocialSecurityData {
  /** acct */
  acct = '';

  /** cityName */
  cityName = '';

  /** empId */
  empId = '';

  /** empName */
  empName = '';

  /** insuranceName */
  insuranceName = '';

  /** ssGroupType */
  ssGroupType = '';
}

class QueryKmCategoryBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class QueryKmCategoryData {
  /** categoryName */
  categoryName = '';

  /** id */
  id = '';

  /** parentCategoryName */
  parentCategoryName = '';

  /** parentId */
  parentId = '';

  /** tenantId */
  tenantId = '';
}

class QueryKnowledgeBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class QueryKnowledgeData {
  /** allowReview */
  allowReview = '';

  /** approvalProcess */
  approvalProcess = '';

  /** categoryName */
  categoryName = '';

  /** content */
  content = '';

  /** creationBy */
  creationBy = '';

  /** creationByName */
  creationByName = '';

  /** creationDate */
  creationDate = '';

  /** creationUser */
  creationUser = '';

  /** id */
  id = '';

  /** isFavorites */
  isFavorites = '';

  /** keywords */
  keywords = '';

  /** kmCategoryFk */
  kmCategoryFk = '';

  /** kmCategoryName */
  kmCategoryName = '';

  /** kmGCategoryFk */
  kmGCategoryFk = '';

  /** kmGPCategoryFk */
  kmGPCategoryFk = '';

  /** kmPCategoryFk */
  kmPCategoryFk = '';

  /** knowledgeType */
  knowledgeType = '';

  /** lastModifiedBy */
  lastModifiedBy = '';

  /** lastModifiedByName */
  lastModifiedByName = '';

  /** lastModifiedDate */
  lastModifiedDate = '';

  /** matchingDegree */
  matchingDegree = '';

  /** name */
  name = '';

  /** source */
  source = '';

  /** status */
  status = '';

  /** subject */
  subject = '';

  /** summary */
  summary = '';

  /** tenantId */
  tenantId = '';

  /** validDateTo */
  validDateTo = '';

  /** viewTimes */
  viewTimes = '';
}

class RespWrapper {
  /** 返回状态code */
  code = undefined;

  /** 具体的返回结果 */
  data = new ClcData();

  /** 错误信息 */
  msg = '';
}

class SmsParam {
  /** accountId */
  accountId = '';

  /** cellphone */
  cellphone = '';

  /** idCardNum */
  idCardNum = '';

  /** openId */
  openId = '';

  /** vcode */
  vcode = '';
}

class SmsResponseBean {
  /** code */
  code = '';

  /** data */
  data = new SmsResponseData();

  /** message */
  message = '';
}

class SmsResponseData {
  /** mobilePhoneNum */
  mobilePhoneNum = '';

  /** result */
  result = '';
}

class TemplateMessageParam {
  /** busId */
  busId = '';

  /** empId */
  empId = '';

  /** first */
  first = '';

  /** id */
  id = '';

  /** idCard */
  idCard = '';

  /** keyword1 */
  keyword1 = '';

  /** keyword2 */
  keyword2 = '';

  /** keyword3 */
  keyword3 = '';

  /** keyword4 */
  keyword4 = '';

  /** messageId */
  messageId = '';

  /** openId */
  openId = '';

  /** remark */
  remark = '';

  /** reservationId */
  reservationId = '';

  /** typeCode */
  typeCode = '';
}

class TestInfo {
  /** id */
  id = undefined;

  /** name */
  name = '';

  /** pwd */
  pwd = '';
}

class ThingItemsBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class ThingItemsData {
  /** key */
  key = '';

  /** value */
  value = '';
}

class ThingsResponseBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class ThingsResponseData {
  /** key */
  key = '';

  /** value */
  value = '';
}

class UploadEntryFileParam {
  /** accountId */
  accountId = '';

  /** businessId */
  businessId = '';

  /** cityCode */
  cityCode = '';

  /** mediaId */
  mediaId = '';

  /** openId */
  openId = '';

  /** type */
  type = '';
}

class UploadEntryFileRespBean {
  /** code */
  code = '';

  /** data */
  data = new UploadEntryFileRespData();

  /** message */
  message = '';
}

class UploadEntryFileRespData {
  /** address */
  address = '';

  /** fileId */
  fileId = '';

  /** idCard */
  idCard = '';

  /** name */
  name = '';

  /** picUrl */
  picUrl = '';

  /** result */
  result = '';

  /** sex */
  sex = '';

  /** validEnd */
  validEnd = '';

  /** validStart */
  validStart = '';
}

class UuidBean {
  /** code */
  code = '';

  /** data */
  data = new UuidData();

  /** message */
  message = '';
}

class UuidData {
  /** msg */
  msg = '';

  /** status */
  status = '';

  /** uuid */
  uuid = '';
}

class VcodeBean {
  /** code */
  code = '';

  /** data */
  data = new VcodeData();

  /** message */
  message = '';
}

class VcodeData {
  /** base64 */
  base64 = '';
}

class WageDetailListPayBean {
  /** payamt */
  payamt = '';

  /** payname */
  payname = '';

  /** paytype */
  paytype = '';
}

class WageDetailListRespBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class WageDetailTotalRespBean {
  /** code */
  code = '';

  /** data */
  data = new WageDetailTotalRespData();

  /** message */
  message = '';
}

class WageDetailTotalRespData {
  /** classId */
  classId = '';

  /** custName */
  custName = '';

  /** empId */
  empId = '';

  /** f1 */
  f1 = '';

  /** f10 */
  f10 = '';

  /** f2 */
  f2 = '';

  /** f3 */
  f3 = '';

  /** sendId */
  sendId = '';

  /** sendMonth */
  sendMonth = '';
}

class WageListRespBean {
  /** code */
  code = '';

  /** data */
  data = [];

  /** message */
  message = '';
}

class WageListRespData {
  /** classId */
  classId = '';

  /** custName */
  custName = '';

  /** empId */
  empId = '';

  /** f1 */
  f1 = '';

  /** f10 */
  f10 = '';

  /** f2 */
  f2 = '';

  /** f3 */
  f3 = '';

  /** pages */
  pages = undefined;

  /** sendId */
  sendId = '';

  /** sendMonth */
  sendMonth = '';

  /** wageBatchStatus */
  wageBatchStatus = '';

  /** wageTaxStatus */
  wageTaxStatus = '';
}

class WageListTotalRespBean {
  /** code */
  code = '';

  /** data */
  data = new WageListTotalRespData();

  /** message */
  message = '';
}

class WageListTotalRespData {
  /** f1 */
  f1 = '';

  /** f10 */
  f10 = '';

  /** f2 */
  f2 = '';

  /** f3 */
  f3 = '';
}

export const pact = {
  AccountInfo,
  AppBindInfo,
  AppointmentCancelBean,
  AppointmentCancelParam,
  AppointmentDetailBean,
  AppointmentDetailData,
  AppointmentListBean,
  AppointmentListData,
  AppointmentNumBean,
  AppointmentNumData,
  AuthParamBean,
  AuthResponseBean,
  AuthResponseData,
  BusinessDetailBean,
  BusinessDetailData,
  BusinessItemBean,
  BusinessItemData,
  BusinessListBean,
  BusinessListData,
  ButlerResponseBean,
  ButlerResponseData,
  CityBean,
  CityData,
  ClcData,
  ClcTemplateInfo,
  CompanyResponseBean,
  CompanyResponseData,
  DynamicFormReqBean,
  EchoFileSubmitReqBean,
  EchoFileSubmitResp,
  EntryParam,
  EntryResponseBean,
  FeedbackBean,
  FeedbackData,
  FollowResponse,
  FollowResponseData,
  FollowerInfo,
  FundData,
  FundDetailBean,
  FundDetailData,
  FundDetailParam,
  FundLiatData,
  FundParam,
  HosResponseBean,
  HosResponseData,
  IdCardComposePram,
  IdCardComposeRespBean,
  InsertParam,
  InsertResponseBean,
  JsonMessage,
  Map,
  MaterBean,
  MaterData,
  MaterialsInfo,
  MenuResponseBean,
  MenuResponseData,
  MoreCityInfo,
  OcrParamBean,
  OcrResponseBean,
  OcrResponseData,
  ParamMap,
  PersonInfoRespBean,
  PersonInfoRespData,
  PersonRelationData,
  PersonSocialSecurityData,
  QueryKmCategoryBean,
  QueryKmCategoryData,
  QueryKnowledgeBean,
  QueryKnowledgeData,
  RespWrapper,
  SmsParam,
  SmsResponseBean,
  SmsResponseData,
  TemplateMessageParam,
  TestInfo,
  ThingItemsBean,
  ThingItemsData,
  ThingsResponseBean,
  ThingsResponseData,
  UploadEntryFileParam,
  UploadEntryFileRespBean,
  UploadEntryFileRespData,
  UuidBean,
  UuidData,
  VcodeBean,
  VcodeData,
  WageDetailListPayBean,
  WageDetailListRespBean,
  WageDetailTotalRespBean,
  WageDetailTotalRespData,
  WageListRespBean,
  WageListRespData,
  WageListTotalRespBean,
  WageListTotalRespData,
};
