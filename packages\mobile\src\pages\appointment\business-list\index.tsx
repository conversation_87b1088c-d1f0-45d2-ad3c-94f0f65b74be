/*
 * @Author: 王正荣
 * @Email: <EMAIL>
 * @Date: 2021-09-18 10:04:39
 * @LastAuthor: 王正荣
 * @LastTime: 2021-12-02 14:31:45
 * @message:
 */
import { Fragment, useState } from 'react'
import { View, Text } from '@tarojs/components'
import { withPage, DateSelect, ListView, usePagination, FormTip, Buttons } from '@components'
import Taro from '@tarojs/taro'
import { pact } from '@apis/pact'
import { getScrollStyle } from '@utils/transforms'
import { getGlobalData } from '@utils'
import dayjs from 'dayjs'
import styles from './index.module.scss'

const itemConfig = [
  { title: '所属类型', key: 'categoryName' },
  { title: '业务大类', key: 'busTypeName' },
  { title: '业务小类', key: 'busSubTypeName' },
  { title: '办理开始时间', key: 'createDt' },
  { title: '办理状态', key: 'businessStatus' }
]

const businessStatusMap = new Map<string, string>([
  ['1', '办理中'],
  ['2', '办理完成']
])

const Index = () => {
  const scrollStyle = getScrollStyle({ top: 256 + 60 })
  const { openId, empId } = getGlobalData<'account'>('account')
  const startDate = dayjs().subtract(3, 'months').add(1, 'days').format('YYYY-MM-DD')
  const endDate = dayjs().format('YYYY-MM-DD')
  const [createDt, setCreateDt] = useState(`${startDate},${endDate}`)
  const list = usePagination(
    async page => {
      const result = await pact.busi.getBusinessList.request({
        pageNo: String(page),
        createDt,
        empId,
        openId
      })
      return result.data
    },
    { deps: [createDt] }
  )

  const detail = (businessId: string) => {
    Taro.navigateTo({ url: `/pages/appointment/business-detail/index?businessId=${businessId}` })
  }

  const classify = (key: string, value: string) => {
    switch(key) {
      case 'businessStatus': return businessStatusMap.get(value)
      case 'createDt': return value.substring(0, 10)
      default: return value
    }
  }

  return (
    <Fragment>
      <DateSelect
        onSelectHandle={e => {
          setCreateDt(`${e.startDate},${e.endDate}`)
        }}
        pickerProps={[{ fields: 'day' }, { fields: 'day' }]}
      />
      <View className={styles.tip}>
        <FormTip tip='提示：查询时间为提交时间' />
      </View>
      <ListView
        style={scrollStyle}
        itemSize={438}
        unlimitedSize
        renderItem={(item,index,id) => (
          <View className={styles.item} id={id}>
            {itemConfig.map(config => (
              <View className={styles.text_wrap} key={config.key}>
                <Text className={styles.title}>{config.title}</Text>
                <Text className={styles.detail}>
                  {classify(config.key, item[config.key])}
                </Text>
              </View>
            ))}
            <View className={styles.btn_wrap}>
              <Buttons
                title='查看明细'
                icon='detail'
                onClick={() => detail(item.businessId)}
              />
            </View>
          </View>
        )}
        {...list}
      />
    </Fragment>
  )
}

export default withPage(Index)
