/**
 * @description 获取易才HRO系统中定点医院的信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityId */
  cityId?: string;
  /** filterValue */
  filterValue?: string;
  /** hosCountyName */
  hosCountyName?: string;
  /** hosLevel */
  hosLevel?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.HosResponseBean;
export const path = '/yc-wepact-mobile/entry/designatedHospitals';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
