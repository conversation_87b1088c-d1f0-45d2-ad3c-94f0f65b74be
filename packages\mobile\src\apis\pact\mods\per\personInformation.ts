/**
 * @description 查询HRO系统中的个人信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.PersonInfoRespBean;
export const path = '/yc-wepact-mobile/per/personInformation';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
