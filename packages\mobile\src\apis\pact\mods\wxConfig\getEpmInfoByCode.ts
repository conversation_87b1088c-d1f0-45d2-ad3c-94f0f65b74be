/**
 * @description 根据code获取emp
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** code */
  code: string;
}

export type Result = defs.pact.JsonMessage;
export const path = '/yc-wepact-mobile/getEpmInfoByCode';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
