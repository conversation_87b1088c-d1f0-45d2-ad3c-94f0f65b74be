/**
 * @description 获取业务大类列表
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.BusinessType>;
export const path = '/wx-ncmp/policy/getBusType';
export const method = 'POST';
export const request = (
  data: defs.ncmp.BusinessType,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.BusinessType,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
