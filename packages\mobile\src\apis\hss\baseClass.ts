class FeedBack {
  /** content */
  content = '';

  /** title */
  title = '';
}

class GeneralRespBean {
  /** code */
  code = '';

  /** data */
  data = new HsAuth();

  /** msg */
  msg = '';
}

class HsAuth {
  /** createTime */
  createTime = '';

  /** customerId */
  customerId = '';

  /** customerName */
  customerName = '';

  /** declareId */
  declareId = '';

  /** hsType */
  hsType = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** idType */
  idType = '';

  /** name */
  name = '';

  /** openid */
  openid = '';

  /** personId */
  personId = '';

  /** phone */
  phone = '';

  /** status1 */
  status1 = '';

  /** status2 */
  status2 = '';

  /** updateTime */
  updateTime = '';

  /** vCode */
  vCode = '';
}

class HsBankInfo {
  /** bankAccount */
  bankAccount = '';

  /** bankName */
  bankName = '';

  /** basicId */
  basicId = undefined;

  /** createTime */
  createTime = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** openBankCity */
  openBankCity = '';

  /** openBankCityid */
  openBankCityid = '';

  /** openBankName */
  openBankName = '';

  /** openBankProvince */
  openBankProvince = '';

  /** openBankProvinceid */
  openBankProvinceid = '';

  /** openid */
  openid = '';

  /** updateBy */
  updateBy = '';

  /** updateTime */
  updateTime = '';
}

class HsBasicInfo {
  /** address */
  address = '';

  /** createTime */
  createTime = '';

  /** email */
  email = '';

  /** hsCity */
  hsCity = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** idType */
  idType = '';

  /** liveChinaDays */
  liveChinaDays = '';

  /** name */
  name = '';

  /** openid */
  openid = '';

  /** phoneNumber */
  phoneNumber = '';

  /** status */
  status = '';

  /** taxYearDays */
  taxYearDays = '';

  /** taxpayerNumber */
  taxpayerNumber = '';

  /** term */
  term = '';

  /** type */
  type = '';

  /** updateBy */
  updateBy = '';

  /** updateTime */
  updateTime = '';
}

class HsData {
  /** data */
  data = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** num */
  num = undefined;

  /** openid */
  openid = '';

  /** payStatus */
  payStatus = '';

  /** payStatusStr */
  payStatusStr = '';

  /** status */
  status = '';

  /** statusStr */
  statusStr = '';

  /** updateBy */
  updateBy = '';

  /** updateTime */
  updateTime = '';
}

class HsDeductionOtherInfo {
  /** allowDeductDonation */
  allowDeductDonation = '';

  /** allowDeductTax */
  allowDeductTax = '';

  /** annuity */
  annuity = '';

  /** basicId */
  basicId = undefined;

  /** healthInsurance */
  healthInsurance = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** openid */
  openid = '';

  /** other */
  other = '';

  /** propertyOldValue */
  propertyOldValue = '';

  /** remarks */
  remarks = '';

  /** taxDelayInsurance */
  taxDelayInsurance = '';

  /** taxPaid */
  taxPaid = '';
}

class HsDeductionSpecialInfo {
  /** attribute1 */
  attribute1 = '';

  /** attribute2 */
  attribute2 = '';

  /** attribute3 */
  attribute3 = '';

  /** attribute4 */
  attribute4 = '';

  /** basicId */
  basicId = undefined;

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** openid */
  openid = '';

  /** segment1 */
  segment1 = '';

  /** segment2 */
  segment2 = '';

  /** segment3 */
  segment3 = '';

  /** segment4 */
  segment4 = '';

  /** segment5 */
  segment5 = '';

  /** segment6 */
  segment6 = '';
}

class HsIncomeInfo {
  /** basicId */
  basicId = undefined;

  /** bonus */
  bonus = '';

  /** city */
  city = '';

  /** draftWages */
  draftWages = '';

  /** franchiseWages */
  franchiseWages = '';

  /** hsYear */
  hsYear = '';

  /** id */
  id = undefined;

  /** idCard */
  idCard = '';

  /** labourWages */
  labourWages = '';

  /** openid */
  openid = '';

  /** salary */
  salary = '';
}

class HsResult {
  /** segment1 */
  segment1 = '';

  /** segment10 */
  segment10 = '';

  /** segment11 */
  segment11 = '';

  /** segment12 */
  segment12 = '';

  /** segment13 */
  segment13 = '';

  /** segment2 */
  segment2 = '';

  /** segment3 */
  segment3 = '';

  /** segment4 */
  segment4 = '';

  /** segment5 */
  segment5 = '';

  /** segment6 */
  segment6 = '';

  /** segment7 */
  segment7 = '';

  /** segment8 */
  segment8 = '';

  /** segment9 */
  segment9 = '';

  /** status */
  status = '';

  /** statusStr */
  statusStr = '';

  /** year */
  year = '';
}

class Map {}

class TemplateMsg {
  /** errorList */
  errorList = new FeedBack();

  /** first */
  first = '';

  /** idCard */
  idCard = '';

  /** keyword1 */
  keyword1 = '';

  /** keyword2 */
  keyword2 = '';

  /** keyword3 */
  keyword3 = '';

  /** keyword4 */
  keyword4 = '';

  /** openid */
  openid = '';

  /** remark */
  remark = '';

  /** typeCode */
  typeCode = '';
}

export const hss = {
  FeedBack,
  GeneralRespBean,
  HsAuth,
  HsBankInfo,
  HsBasicInfo,
  HsData,
  HsDeductionOtherInfo,
  HsDeductionSpecialInfo,
  HsIncomeInfo,
  HsResult,
  Map,
  TemplateMsg,
};
