class BindAccountParams {
  /** 所属平台的企业id */
  company = '';

  /** 员工姓名 */
  empName = '';

  /** 身份证号 */
  idCardNum = '';

  /** 手机号码 */
  mobilePhoneNum = '';

  /** 手机验证码 */
  mobileVerifyCode = '';

  /** 平台 1:公众号 2:企微 3:钉钉 4:飞书  */
  platform = undefined;

  /** 所属平台的openid */
  platformOpenid = '';
}

class BindAccountResp {
  /** accountId */
  accountId = '';

  /** accountInfoId */
  accountInfoId = '';

  /** empId */
  empId = '';

  /** 错误信息 */
  errorMsg = '';

  /** 全平台用户token */
  globalToken = '';
}

class BindVerifyCodeResp {
  /** 报错信息 */
  errorMsg = '';

  /** 0: 失败  1: 成功 */
  success = undefined;
}

class GlobalResult {
  /** 数据对象 */
  data = new BindAccountResp();

  /** 正常返回0，错误时返回非0的纯数字字符串 */
  errorCode = '';

  /** 正常返回空，错误时返回错误信息 */
  errorMessage = '';

  /** 正常返回true，错误时返回false */
  success = false;
}

class MobileVerifyParams {
  /** 身份证号 */
  idCardNum = '';

  /** 手机号码 */
  mobilePhoneNum = '';

  /** 平台 1:公众号 2:企微 3:钉钉 4:飞书  */
  platform = undefined;

  /** 所属平台的openid */
  platformOpenid = '';

  /** 图片验证码 */
  vcode = '';
}

class QySdkSign {
  /** 应用id */
  agentId = '';

  /** 随机数 */
  noncestr = '';

  /** 签名 */
  signature = '';

  /** 时间戳 */
  timestamp = '';
}

class TokenByCode {
  /** 业务平台accountId，只特定情况下才会有 */
  accountId = '';

  /** 企业唯一id */
  cropId = '';

  /** 业务token，有正确绑定关系时才返回 */
  globalToken = '';

  /** 是否绑定 */
  isBind = false;

  /** 用户唯一id */
  platformOpenid = '';

  /** 是否签约 */
  sign = false;
}

class VcodeResp {
  /** 图片验证码 */
  base64Img = '';

  /** 错误消息 */
  errorMsg = '';
}

export const auth = {
  BindAccountParams,
  BindAccountResp,
  BindVerifyCodeResp,
  GlobalResult,
  MobileVerifyParams,
  QySdkSign,
  TokenByCode,
  VcodeResp,
};
