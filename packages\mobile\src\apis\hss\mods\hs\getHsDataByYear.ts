/**
 * @description 根据年份查询汇算信息详情
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** openid */
  openid: string;
  /** year */
  year: string;
}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/hsInfo/getHsDataByYear/{openid}/{year}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
