/**
 * @description 新增汇算基本信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/api/insertBasicInfo';
export const method = 'POST';
export const request = (
  data: defs.hss.HsBasicInfo,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.hss.HsBasicInfo,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
