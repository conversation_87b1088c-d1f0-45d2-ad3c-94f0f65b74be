/**
 * @description 下载政策附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** fileName */
  fileName: string;
  /** filePath */
  filePath: string;
}

export type Result = any;
export const path = '/wx-ncmp/policy/downloadPolicyFile';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
