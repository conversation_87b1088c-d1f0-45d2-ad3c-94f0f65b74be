/**
 * @description 简单的调用示例，前端不要用这接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<string>;
export const path = '/wx-ncmp/eos/attendance/test';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
