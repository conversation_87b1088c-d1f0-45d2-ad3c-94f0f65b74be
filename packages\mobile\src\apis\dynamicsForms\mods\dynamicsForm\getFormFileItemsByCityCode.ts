/**
 * @description 获取城市的动态文件条目项
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** cityCode */
  cityCode: string;
  /** openId */
  openId: string;
}

export type Result = Array<defs.dynamicsForms.FormFileItemBean>;
export const path =
  '/dynamics-form-service/dynamics_form/fileItem/{cityCode}/{openId}';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
