/**
 * @description 预约详情接口
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** bookingId */
  bookingId?: string;
  /** openId */
  openId?: string;
}

export type Result = defs.pact.AppointmentDetailBean;
export const path = '/yc-wepact-mobile/appoint/getAppointmentDetail';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
