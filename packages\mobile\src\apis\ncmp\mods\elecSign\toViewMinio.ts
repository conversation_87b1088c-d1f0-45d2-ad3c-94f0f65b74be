/**
 * @description 查看补证件上传附件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** filePath */
  filePath: string;
}

export type Result = any;
export const path = '/wx-ncmp/elecsign/view';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
