/**
 * @description 查看电子离职证明
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.EcQuit>;
export const path = '/wx-ncmp/elecsign/getQuitCert';
export const method = 'POST';
export const request = (
  data: defs.ncmp.EcQuit,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.EcQuit,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
