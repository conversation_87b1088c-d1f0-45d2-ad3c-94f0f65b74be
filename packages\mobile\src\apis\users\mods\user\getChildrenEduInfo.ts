/**
 * @description getChildrenEduInfo
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** childrenId */
  childrenId: string;
}

export type Result = defs.users.ExecuteResult<defs.users.ChildrenEducation>;
export const path = '/user-server/api/getChildrenEduInfo';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
