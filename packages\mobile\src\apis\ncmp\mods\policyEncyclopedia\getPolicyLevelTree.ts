/**
 * @description 政策目录导航
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<Array<defs.ncmp.PolicyLevelTree>>;
export const path = '/wx-ncmp/policyEncyclopedia/getPolicyLevelTree';
export const method = 'POST';
export const request = (
  data: defs.ncmp.QueryPolicyEncyclopedia,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.QueryPolicyEncyclopedia,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
