/**
 * @description getCustName
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** employeeId */
  employeeId?: string;
}

export type Result = defs.users.ExecuteResult<Array>;
export const path = '/user-server/api/getCustName';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
