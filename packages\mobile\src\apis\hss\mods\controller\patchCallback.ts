/**
 * @description paySuccess
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = string;
export const path = '/yc-hs/api/callback';
export const method = 'PATCH';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, { method: 'PATCH', ...options });
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'PATCH', ...options });
