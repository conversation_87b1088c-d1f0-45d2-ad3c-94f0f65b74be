/**
 * @description 签协议
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.hss.GeneralRespBean<string>;
export const path = '/yc-hs/api/updateAuthByIDCard';
export const method = 'POST';
export const request = (
  data: defs.hss.HsAuth,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.hss.HsAuth,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
