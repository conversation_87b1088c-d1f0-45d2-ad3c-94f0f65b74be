/**
 * @description 获取钉钉JSAPI签名 dd.config
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** corpId */
  corpId?: string;
  /** 页面地址，#号之前的部分 */
  pageUrl: string;
}

export type Result = defs.auth.GlobalResult<defs.auth.QySdkSign>;
export const path = '/enterprise-auth/web/ding/getConfigSignature';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
