// 材料上传组件样式
.container {
  background: #ffffff;
  border-radius: 12px;
  margin: 16px 0;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
}

// 头部信息
.header {
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f5f5f5;
}

.materialInfo {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.materialName {
  font-size: 32px;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
  
  .required {
    color: #ff4d4f;
    margin-left: 4px;
  }
}

.materialDetails {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.detail {
  font-size: 24px;
  color: #666666;
  background: #f8f9fa;
  padding: 4px 12px;
  border-radius: 16px;
  line-height: 1.3;
}

// 文件列表
.fileList {
  margin-bottom: 20px;
}

.fileItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  margin-bottom: 12px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  transition: all 0.2s ease;
  
  &:hover {
    background: #f0f0f0;
    border-color: #d9d9d9;
  }
  
  &:last-child {
    margin-bottom: 0;
  }
}

.fileInfo {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.fileIcon {
  width: 48px;
  height: 48px;
  margin-right: 12px;
  border-radius: 6px;
  flex-shrink: 0;
}

.fileDetails {
  flex: 1;
  min-width: 0;
}

.fileName {
  font-size: 28px;
  color: #333333;
  font-weight: 500;
  line-height: 1.3;
  display: block;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fileSize {
  font-size: 22px;
  color: #999999;
  margin-top: 4px;
  display: block;
}

.uploadTime {
  font-size: 20px;
  color: #cccccc;
  margin-top: 2px;
  display: block;
}

// 文件状态
.fileStatus {
  flex-shrink: 0;
  margin-left: 12px;
}

.uploading {
  font-size: 24px;
  color: #1890ff;
  animation: pulse 1.5s infinite;
}

.error {
  font-size: 24px;
  color: #ff4d4f;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

// 操作按钮
.actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

.actionBtn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background: #f0f8ff;
  border: 1px solid #d6e4ff;
  border-radius: 6px;
  font-size: 22px;
  color: #1890ff;
  min-width: auto;
  height: auto;
  line-height: 1.2;
  
  &:hover {
    background: #e6f7ff;
    border-color: #91d5ff;
  }
  
  &::after {
    display: none;
  }
}

.deleteBtn {
  @extend .actionBtn;
  background: #fff2f0;
  border-color: #ffccc7;
  color: #ff4d4f;
  
  &:hover {
    background: #fff1f0;
    border-color: #ffa39e;
  }
}

.actionIcon {
  width: 24px;
  height: 24px;
}

// 上传区域
.uploadSection {
  text-align: center;
  padding: 20px;
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #1890ff;
    background: #f0f8ff;
  }
}

.uploadBtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  background: transparent;
  border: none;
  padding: 16px;
  width: 100%;
  min-width: auto;
  height: auto;
  line-height: 1.2;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &::after {
    display: none;
  }
}

.uploadIcon {
  width: 48px;
  height: 48px;
  opacity: 0.6;
}

.uploadText {
  font-size: 28px;
  color: #666666;
  font-weight: 500;
}

.uploadTip {
  font-size: 22px;
  color: #999999;
  margin-top: 8px;
  display: block;
}

// 响应式设计
@media (max-width: 480px) {
  .container {
    margin: 12px 0;
    padding: 16px;
  }
  
  .materialName {
    font-size: 30px;
  }
  
  .detail {
    font-size: 22px;
    padding: 3px 10px;
  }
  
  .fileName {
    font-size: 26px;
  }
  
  .fileSize {
    font-size: 20px;
  }
  
  .actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .actionBtn,
  .deleteBtn {
    font-size: 20px;
    padding: 6px 10px;
  }
  
  .uploadText {
    font-size: 26px;
  }
}

// 空状态
.emptyState {
  text-align: center;
  padding: 40px 20px;
  color: #999999;
  
  .emptyIcon {
    width: 64px;
    height: 64px;
    opacity: 0.3;
    margin-bottom: 16px;
  }
  
  .emptyText {
    font-size: 26px;
    line-height: 1.4;
  }
}

// 加载状态
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  
  .loadingIcon {
    width: 32px;
    height: 32px;
    animation: spin 1s linear infinite;
    margin-right: 8px;
  }
  
  .loadingText {
    font-size: 24px;
    color: #666666;
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
