/**
 * @description 通过accountId查询最新入职信息
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {
  /** accountId */
  accountId: string;
}

export type Result = defs.ncmp.WechatHiresepDTO;
export const path = '/wx-ncmp/hiresep/lastestHiresep';
export const method = 'GET';
export const request = (data: Params, options?: Taro.request.CommonOption) =>
  fetch<Result, Params>(path, data, options);
export const useRequest = (
  data: Params,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, options);
