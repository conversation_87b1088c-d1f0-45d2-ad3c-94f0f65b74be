import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import MaterialUpload, { MaterialData, FileStatus } from '../index'

// Mock Taro
jest.mock('@tarojs/taro', () => ({
  showToast: jest.fn(),
  previewImage: jest.fn()
}))

// Mock APIs
jest.mock('@apis/upload', () => ({
  upload: {
    fileUploader: {
      uploadAppointmentFileByForm: {
        request: jest.fn()
      },
      deleteAppointmentFile: {
        request: jest.fn()
      }
    }
  }
}))

// Mock utils
jest.mock('@utils', () => ({
  getGlobalData: jest.fn(() => ({
    openId: 'test-openid',
    accountId: 'test-accountid'
  })),
  isWx: jest.fn(() => false)
}))

// Mock wx-config
jest.mock('@utils/wx-config', () => ({
  wxConfigManager: {
    chooseImage: jest.fn(),
    uploadImage: jest.fn(),
    downloadImage: jest.fn(),
    previewImage: jest.fn()
  }
}))

describe('MaterialUpload', () => {
  const mockMaterial: MaterialData = {
    materialsId: '001',
    materialsName: '身份证',
    isOriginal: '1',
    materialsAccount: 1,
    isReturn: '1',
    isRequired: true,
    files: []
  }

  const defaultProps = {
    material: mockMaterial,
    businessId: 'test-business-id'
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render material information correctly', () => {
    render(<MaterialUpload {...defaultProps} />)
    
    expect(screen.getByText('身份证')).toBeInTheDocument()
    expect(screen.getByText('*')).toBeInTheDocument() // Required indicator
    expect(screen.getByText('原件')).toBeInTheDocument()
    expect(screen.getByText('数量：1')).toBeInTheDocument()
    expect(screen.getByText('需返还')).toBeInTheDocument()
  })

  it('should render upload button when not readonly', () => {
    render(<MaterialUpload {...defaultProps} />)
    
    expect(screen.getByText('上传材料')).toBeInTheDocument()
    expect(screen.getByText('支持图片格式，最多上传9个文件')).toBeInTheDocument()
  })

  it('should not render upload button when readonly', () => {
    render(<MaterialUpload {...defaultProps} readonly={true} />)
    
    expect(screen.queryByText('上传材料')).not.toBeInTheDocument()
  })

  it('should render uploaded files correctly', () => {
    const materialWithFiles: MaterialData = {
      ...mockMaterial,
      files: [
        {
          id: '1',
          name: 'test-image.jpg',
          url: 'http://example.com/test-image.jpg',
          size: 1024,
          type: 'image',
          status: FileStatus.SUCCESS,
          uploadTime: '2023-01-01 12:00:00'
        }
      ]
    }

    render(<MaterialUpload material={materialWithFiles} businessId="test-id" />)
    
    expect(screen.getByText('test-image.jpg')).toBeInTheDocument()
    expect(screen.getByText('1.0KB')).toBeInTheDocument()
    expect(screen.getByText('2023-01-01 12:00:00')).toBeInTheDocument()
    expect(screen.getByText('预览')).toBeInTheDocument()
    expect(screen.getByText('下载')).toBeInTheDocument()
    expect(screen.getByText('删除')).toBeInTheDocument()
  })

  it('should show uploading status correctly', () => {
    const materialWithUploadingFile: MaterialData = {
      ...mockMaterial,
      files: [
        {
          id: '1',
          name: 'uploading-image.jpg',
          status: FileStatus.UPLOADING,
          progress: 50
        }
      ]
    }

    render(<MaterialUpload material={materialWithUploadingFile} businessId="test-id" />)
    
    expect(screen.getByText('上传中...')).toBeInTheDocument()
  })

  it('should show error status correctly', () => {
    const materialWithErrorFile: MaterialData = {
      ...mockMaterial,
      files: [
        {
          id: '1',
          name: 'error-image.jpg',
          status: FileStatus.ERROR
        }
      ]
    }

    render(<MaterialUpload material={materialWithErrorFile} businessId="test-id" />)
    
    expect(screen.getByText('上传失败')).toBeInTheDocument()
  })

  it('should call onFileChange when files change', () => {
    const mockOnFileChange = jest.fn()
    
    render(
      <MaterialUpload 
        {...defaultProps} 
        onFileChange={mockOnFileChange}
      />
    )

    // This would be triggered by file upload/delete operations
    // The actual implementation would call onFileChange when files state changes
    expect(mockOnFileChange).toHaveBeenCalledWith([])
  })

  it('should format file size correctly', () => {
    const materialWithFiles: MaterialData = {
      ...mockMaterial,
      files: [
        {
          id: '1',
          name: 'small.jpg',
          size: 500,
          status: FileStatus.SUCCESS
        },
        {
          id: '2',
          name: 'medium.jpg',
          size: 1536, // 1.5KB
          status: FileStatus.SUCCESS
        },
        {
          id: '3',
          name: 'large.jpg',
          size: 2097152, // 2MB
          status: FileStatus.SUCCESS
        }
      ]
    }

    render(<MaterialUpload material={materialWithFiles} businessId="test-id" />)
    
    expect(screen.getByText('500B')).toBeInTheDocument()
    expect(screen.getByText('1.5KB')).toBeInTheDocument()
    expect(screen.getByText('2.0MB')).toBeInTheDocument()
  })

  it('should disable upload button when uploading', () => {
    render(<MaterialUpload {...defaultProps} />)
    
    const uploadButton = screen.getByText('上传材料').closest('button')
    expect(uploadButton).not.toBeDisabled()
    
    // When uploading state is true, button should be disabled
    // This would need to be tested through actual upload flow
  })

  it('should disable upload button when max files reached', () => {
    const materialWithMaxFiles: MaterialData = {
      ...mockMaterial,
      files: Array.from({ length: 9 }, (_, i) => ({
        id: `${i + 1}`,
        name: `file-${i + 1}.jpg`,
        status: FileStatus.SUCCESS
      }))
    }

    render(<MaterialUpload material={materialWithMaxFiles} businessId="test-id" />)
    
    const uploadButton = screen.getByText('上传材料').closest('button')
    expect(uploadButton).toBeDisabled()
  })
})
