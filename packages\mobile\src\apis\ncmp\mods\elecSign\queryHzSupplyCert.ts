/**
 * @description 获取需要上传的证件
 */
import { useRequest as useFetch, request as fetch } from '@utils/request';

export class Params {}

export type Result = defs.ncmp.HttpResult<defs.ncmp.HzSupplyCert>;
export const path = '/wx-ncmp/elecsign/queryHzSupplyCert';
export const method = 'POST';
export const request = (
  data: defs.ncmp.HzSupplyCert,
  options?: Taro.request.CommonOption,
) => fetch<Result, Params>(path, data, { method: 'POST', ...options });
export const useRequest = (
  data: defs.ncmp.HzSupplyCert,
  options?: Taro.request.CommonUseRequestOption<Result>,
) => useFetch<Result, Params>(path, data, { method: 'POST', ...options });
