import { useState, useEffect, useCallback } from 'react'
import { View, Text } from '@tarojs/components'
import Taro from '@tarojs/taro'
import { pact } from '@apis/pact'
import { upload } from '@apis/upload'
import { getGlobalData } from '@utils'
import MaterialUpload, { MaterialData, UploadedFile } from '../material-upload'
import styles from './index.module.scss'

// 材料列表组件属性
export interface MaterialListProps {
  /** 业务子类型ID */
  busSubtypeId?: string
  /** 业务UUID */
  businessId: string
  /** 是否只读模式 */
  readonly?: boolean
  /** 材料变化回调 */
  onMaterialChange?: (materials: MaterialData[]) => void
  /** 自定义样式类名 */
  className?: string
}

const MaterialList: React.FC<MaterialListProps> = ({
  busSubtypeId,
  businessId,
  readonly = false,
  onMaterialChange,
  className = ''
}) => {
  const [materials, setMaterials] = useState<MaterialData[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string>('')
  const { openId, accountId } = getGlobalData<'account'>('account')

  // 获取材料列表
  const fetchMaterials = useCallback(async () => {
    if (!busSubtypeId) return

    setLoading(true)
    setError('')

    try {
      const result = await pact.appoint.getMaterialList.request({
        subTypeId: busSubtypeId,
        openId
      })

      if (result.code === '200' && result.data) {
        const materialList: MaterialData[] = result.data.map(item => ({
          materialsId: item.materialsInfo?.materialsId || '',
          materialsName: item.materialsInfo?.materialsName || '',
          isOriginal: item.materialsInfo?.isOriginal || '0',
          materialsAccount: item.materialsAccount || 1,
          isReturn: item.isReturn?.toString() || '0',
          isRequired: item.materialsAccount > 0, // 根据数量判断是否必填
          files: []
        }))

        setMaterials(materialList)
        
        // 获取已上传的文件
        await fetchUploadedFiles(materialList)
      } else {
        throw new Error(result.message || '获取材料列表失败')
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取材料列表失败'
      setError(errorMsg)
      console.error('获取材料列表失败:', err)
      Taro.showToast({ title: errorMsg, icon: 'none' })
    } finally {
      setLoading(false)
    }
  }, [busSubtypeId, openId])

  // 获取已上传的文件
  const fetchUploadedFiles = useCallback(async (materialList: MaterialData[]) => {
    try {
      const result = await upload.fileUploader.fetchAppointmentFile.request({
        uuid: businessId,
        accountId
      })

      if (result.code === '200' && result.resultObj) {
        const uploadedFiles = result.resultObj

        // 将文件分组到对应的材料中
        const updatedMaterials = materialList.map(material => {
          const materialFiles = uploadedFiles
            .filter(file => file.materialsId === material.materialsId)
            .map(file => ({
              id: file.ebmBookingImageId?.toString(),
              name: file.fileName || '',
              url: file.fileUrl,
              size: file.fileSize,
              type: file.fileType || 'image',
              status: 'success' as const,
              uploadTime: file.createTime
            }))

          return {
            ...material,
            files: materialFiles
          }
        })

        setMaterials(updatedMaterials)
      }
    } catch (err) {
      console.error('获取已上传文件失败:', err)
    }
  }, [businessId, accountId])

  // 处理文件变化
  const handleFileChange = useCallback((materialId: string, files: UploadedFile[]) => {
    setMaterials(prev => prev.map(material => 
      material.materialsId === materialId 
        ? { ...material, files }
        : material
    ))
  }, [])

  // 初始化加载
  useEffect(() => {
    fetchMaterials()
  }, [fetchMaterials])

  // 材料变化时通知父组件
  useEffect(() => {
    onMaterialChange?.(materials)
  }, [materials, onMaterialChange])

  // 重新加载
  const handleRetry = useCallback(() => {
    fetchMaterials()
  }, [fetchMaterials])

  // 渲染加载状态
  if (loading) {
    return (
      <View className={`${styles.container} ${className}`}>
        <View className={styles.loading}>
          <Text className={styles.loadingText}>加载材料列表中...</Text>
        </View>
      </View>
    )
  }

  // 渲染错误状态
  if (error) {
    return (
      <View className={`${styles.container} ${className}`}>
        <View className={styles.error}>
          <Text className={styles.errorText}>{error}</Text>
          <View className={styles.retryBtn} onClick={handleRetry}>
            <Text className={styles.retryText}>重试</Text>
          </View>
        </View>
      </View>
    )
  }

  // 渲染空状态
  if (materials.length === 0) {
    return (
      <View className={`${styles.container} ${className}`}>
        <View className={styles.empty}>
          <Text className={styles.emptyText}>暂无材料信息</Text>
          {busSubtypeId && (
            <Text className={styles.emptyTip}>
              请先选择业务类型和业务项目
            </Text>
          )}
        </View>
      </View>
    )
  }

  return (
    <View className={`${styles.container} ${className}`}>
      {/* 材料列表头部 */}
      <View className={styles.header}>
        <Text className={styles.title}>材料清单</Text>
        <Text className={styles.subtitle}>
          共 {materials.length} 项材料
          {materials.filter(m => m.isRequired).length > 0 && 
            `，其中 ${materials.filter(m => m.isRequired).length} 项必填`
          }
        </Text>
      </View>

      {/* 材料项列表 */}
      <View className={styles.materialList}>
        {materials.map((material, index) => (
          <View key={material.materialsId} className={styles.materialItem}>
            <View className={styles.materialIndex}>
              <Text className={styles.indexNumber}>{index + 1}</Text>
            </View>
            <View className={styles.materialContent}>
              <MaterialUpload
                material={material}
                businessId={businessId}
                readonly={readonly}
                onFileChange={(files) => handleFileChange(material.materialsId, files)}
              />
            </View>
          </View>
        ))}
      </View>

      {/* 底部提示 */}
      <View className={styles.footer}>
        <Text className={styles.footerTip}>
          提示：上述材料仅供参考，实际提交材料以易才管家推送为准
        </Text>
      </View>
    </View>
  )
}

export default MaterialList
